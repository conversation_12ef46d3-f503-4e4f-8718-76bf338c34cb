#!/usr/bin/env python3
"""
Test script for the dashboard functionality
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path

def test_dashboard():
    """Test the dashboard generation"""
    print("🧪 Testing dashboard generation...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process sequences (limit to first 5 for testing)
    sequences_data = []
    for seq_id, events in list(touch_data.items())[:5]:
        if len(events) < 2:
            continue
        
        print(f"   Processing sequence {seq_id}...")
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Processed {len(sequences_data)} sequences")
    
    # Test spatial features bucket
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    base_name = selected_file.stem
    
    print(f"🎨 Testing dashboard generation for {bucket_info['name']}...")
    
    # Generate dashboard
    try:
        dashboard_file = validator.create_dashboard_html(sequences_data, bucket_key, bucket_info, base_name)
        print(f"✅ Dashboard created: {dashboard_file}")
    except Exception as e:
        print(f"❌ Dashboard generation failed: {e}")
        return
    
    # Generate individual plots
    print(f"🔍 Testing individual plot generation...")
    for seq_data in sequences_data[:3]:  # Test first 3
        seq_id = seq_data['sequence_id']
        plot_file = f"test_validation_{base_name}_{bucket_key}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file)
            print(f"   ✅ Sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")
    
    print(f"\n🎉 Dashboard test complete!")
    print(f"📂 Generated files:")
    print(f"   🎯 Dashboard: {dashboard_file}")
    print(f"   📊 Individual plots: test_validation_*_seq_*.html")

if __name__ == "__main__":
    test_dashboard()
