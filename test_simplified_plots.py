#!/usr/bin/env python3
"""
Test script for the simplified path visualization with professional colors
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def test_simplified_visualization():
    """Test the simplified individual sequence plots with professional colors"""
    print("🎨 Testing simplified path visualization with professional colors...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process first few sequences for testing
    sequences_data = []
    for seq_id, events in list(touch_data.items())[:3]:  # Test first 3 sequences
        if len(events) < 2:
            continue
        
        print(f"   Processing sequence {seq_id}...")
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Processed {len(sequences_data)} sequences")
    
    # Test spatial features bucket with simplified visualization
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    print(f"🎨 Testing simplified individual plots...")
    
    # Generate simplified individual plots
    generated_files = []
    for seq_data in sequences_data:
        seq_id = seq_data['sequence_id']
        plot_file = f"simplified_{base_name}_{bucket_key}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(
                seq_data, bucket_key, bucket_info, plot_file, base_name
            )
            generated_files.append(output_file)
            print(f"   ✅ Simplified sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")
            import traceback
            traceback.print_exc()
    
    if generated_files:
        # Open the first simplified plot
        first_plot = generated_files[0]
        print(f"\n🌐 Opening simplified plot in browser: {first_plot}")
        try:
            plot_path = os.path.abspath(first_plot)
            webbrowser.open(f"file://{plot_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    print(f"\n🎉 Simplified path visualization test complete!")
    print(f"📂 Generated simplified files:")
    for file in generated_files:
        print(f"   📊 {file}")
    
    print(f"\n📋 Simplified Features:")
    print(f"   🎯 Side-by-side path comparison (no feature bars)")
    print(f"   🎨 Professional color scheme")
    print(f"   📊 Clean layout focusing on path visualization")
    print(f"   📈 Path statistics in subtitle")
    print(f"   🔍 Interactive zoom/pan capabilities")

def test_dashboard_with_filename():
    """Test the updated dashboard with filename in title"""
    print("\n📊 Testing dashboard with filename display...")
    
    validator = SimpleFeatureValidator()
    files = validator.get_available_files()
    
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    
    # Load and process data
    touch_data = validator.load_json_data(selected_file)
    sequences_data = []
    
    for seq_id, events in list(touch_data.items())[:5]:  # Process first 5 for demo
        if len(events) < 2:
            continue
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    # Generate dashboard for spatial features
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    try:
        dashboard_file = validator.create_dashboard_html(sequences_data, bucket_key, bucket_info, base_name)
        print(f"✅ Generated dashboard: {dashboard_file}")
        
        # Open dashboard
        dashboard_path = os.path.abspath(dashboard_file)
        webbrowser.open(f"file://{dashboard_path}")
        print(f"🌐 Opened dashboard with filename in title")
        
    except Exception as e:
        print(f"❌ Dashboard generation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simplified_visualization()
    test_dashboard_with_filename()
