# Feature Validation Tool - Usage Guide

## Overview
The Feature Validation Tool provides comprehensive quality assurance and validation capabilities for touch event data in the coloring behavioral analysis system. It compares raw vs. processed features and identifies data quality issues.

## Fixed Issues
✅ **KeyError: 'x' - RESOLVED**
- The original error was caused by trying to access 'x' and 'y' columns from an empty DataFrame when velocity spikes were calculated
- Fixed by adding proper error handling and checking for column existence before accessing
- Added comprehensive try-catch blocks to handle edge cases gracefully

## Installation Requirements
```bash
# Required packages
pip install pandas numpy matplotlib seaborn

# Optional packages (for enhanced features)
pip install plotly streamlit  # For interactive dashboards
```

## Usage Options

### 1. Static Visualization (Recommended)
Generates PNG plots for each sequence with side-by-side raw vs processed comparison:
```bash
python3 feature_validation.py --vis static --raw
```
**Output:** `validation_plot_seq_*.png` files

### 2. Summary Report Only
Generates a comprehensive text summary without plots:
```bash
python3 feature_validation.py --vis summary --raw
```

### 3. HTML Dashboard
Creates an interactive HTML dashboard with embedded plots:
```bash
python3 feature_validation.py --vis html --raw
```
**Output:** `validation_dashboard.html` (open in web browser)

### 4. Animation (Advanced)
Creates animated GIF visualizations showing touch sequence progression:
```bash
python3 feature_validation.py --vis animation --raw
```
**Output:** `validation_animation_seq_*.gif` files

### 5. Streamlit Dashboard (If Available)
Interactive web dashboard (requires streamlit installation):
```bash
python3 feature_validation.py --vis dashboard --raw
```

## Key Features

### Data Quality Detection
- **Duplicate Timestamps**: Identifies repeated time values
- **Coordinate Outliers**: Flags coordinates outside normal ranges (>2000px)
- **Erratic Velocity Spikes**: Detects sudden velocity changes (>1000px/s)

### Feature Comparison
- **Raw Features**: Direct calculations from touch events
- **Processed Features**: Features extracted by ColoringFeatureExtractor
- **Side-by-Side Visualization**: Easy comparison of raw vs processed data

### Sequence Analysis
- **Duration Analysis**: Time span of touch sequences
- **Path Length**: Total distance traveled
- **Velocity Patterns**: Speed and acceleration analysis
- **Sequence Types**: Tap, Drag, Hold classification

## Output Files

### Generated Plots
- `validation_plot_seq_1.png` through `validation_plot_seq_33.png`
- Each plot shows raw data (left) and processed data (right)
- Abnormalities highlighted in different colors:
  - 🔴 Red: Coordinate outliers
  - 🟠 Orange: Erratic velocity spikes
  - 🔵 Blue: Normal touch path

### HTML Dashboard
- `validation_dashboard.html`: Interactive web interface
- Click plot links to view individual sequence visualizations
- Summary statistics and data quality metrics

## Troubleshooting

### Common Issues
1. **Script hangs**: Use static visualization instead of interactive modes
2. **Missing dependencies**: Install required packages with pip
3. **No plots displayed**: Plots are saved to files instead of shown on screen
4. **Empty sequences**: Tool automatically skips sequences with <2 events

### Performance Tips
- Use `--vis summary` for quick analysis without generating plots
- HTML dashboard limits to first 10 sequences for performance
- Static visualization is fastest for large datasets

## Example Output
```
================================================================================
FEATURE VALIDATION SUMMARY REPORT
================================================================================
Total sequences analyzed: 33

Data Quality Issues:
  - Duplicate timestamps: 0
  - Coordinate outliers: 0
  - Erratic velocity spikes: 1156

Sequence Durations (Raw):
  - Mean: 1.818s
  - Min: 0.033s
  - Max: 14.816s

Feature Extraction Success Rate: 100.0% (33/33)

Sequence Types:
  - Tap: 3
  - Drag: 29
  - Hold: 1
================================================================================
```

## Integration with ColoringFeatureExtractor
The tool automatically uses the `ColoringFeatureExtractor` class to:
- Clean and preprocess touch sequences
- Extract 25 behavioral features
- Compare raw vs processed feature calculations
- Validate feature extraction pipeline accuracy
