#!/usr/bin/env python3
"""
Validation Plot Viewer - Interactive tool for manual validation of ColoringFeatureExtractor
"""

import os
import sys
import subprocess
from pathlib import Path

def get_validation_plots():
    """Get all validation plot files sorted by sequence number"""
    plots = []
    for file in Path('.').glob('validation_*.png'):
        # Extract sequence number from filename
        try:
            seq_num = int(file.name.split('_seq_')[1].split('.')[0])
            plots.append((seq_num, file))
        except (IndexError, ValueError):
            continue
    
    # Sort by sequence number
    plots.sort(key=lambda x: x[0])
    return [plot[1] for plot in plots]

def open_image(filepath):
    """Open image file with default system viewer"""
    try:
        if sys.platform.startswith('darwin'):  # macOS
            subprocess.run(['open', str(filepath)])
        elif sys.platform.startswith('linux'):  # Linux
            subprocess.run(['xdg-open', str(filepath)])
        elif sys.platform.startswith('win'):  # Windows
            subprocess.run(['start', str(filepath)], shell=True)
        else:
            print(f"Please manually open: {filepath}")
    except Exception as e:
        print(f"Error opening {filepath}: {e}")

def main():
    print("🎯 COLORINGFEATUREEXTRACTOR VALIDATION VIEWER")
    print("=" * 60)
    
    plots = get_validation_plots()
    
    if not plots:
        print("❌ No validation plots found!")
        print("Run: python feature_validation.py --vis static --raw")
        return
    
    print(f"📊 Found {len(plots)} validation plots")
    print("\n📋 VALIDATION CHECKLIST:")
    print("   ✓ Raw vs Processed path_length accuracy")
    print("   ✓ Sequence type classification (Tap/Drag/Hold)")
    print("   ✓ Drag area bounding box visualization")
    print("   ✓ Kinematic features (velocity, acceleration)")
    print("   ✓ Data quality issues (outliers, spikes)")
    
    print(f"\n🔍 NAVIGATION COMMANDS:")
    print("   [Enter] - Next plot")
    print("   'b' - Previous plot")
    print("   'j [num]' - Jump to sequence number")
    print("   'l' - List all sequences")
    print("   'q' - Quit")
    
    current_idx = 0
    
    while current_idx < len(plots):
        plot_file = plots[current_idx]
        seq_num = int(plot_file.name.split('_seq_')[1].split('.')[0])
        
        print(f"\n📈 Viewing: Sequence {seq_num} ({current_idx + 1}/{len(plots)})")
        print(f"   File: {plot_file.name}")
        
        # Open the plot
        open_image(plot_file)
        
        # Get user input
        try:
            user_input = input("\nCommand [Enter=next, b=back, j=jump, l=list, q=quit]: ").strip().lower()
            
            if user_input == '' or user_input == 'n':
                current_idx += 1
            elif user_input == 'b':
                current_idx = max(0, current_idx - 1)
            elif user_input.startswith('j'):
                try:
                    target_seq = int(user_input.split()[1])
                    # Find the index of the target sequence
                    for i, plot in enumerate(plots):
                        plot_seq = int(plot.name.split('_seq_')[1].split('.')[0])
                        if plot_seq == target_seq:
                            current_idx = i
                            break
                    else:
                        print(f"❌ Sequence {target_seq} not found!")
                except (IndexError, ValueError):
                    print("❌ Invalid jump command. Use: j [sequence_number]")
            elif user_input == 'l':
                print("\n📋 Available sequences:")
                for i, plot in enumerate(plots):
                    seq_num = int(plot.name.split('_seq_')[1].split('.')[0])
                    marker = "👉" if i == current_idx else "  "
                    print(f"   {marker} {i+1:2d}. Sequence {seq_num}")
                input("\nPress Enter to continue...")
            elif user_input == 'q':
                break
            else:
                print("❌ Unknown command!")
                
        except KeyboardInterrupt:
            print("\n\n👋 Validation session ended.")
            break
    
    if current_idx >= len(plots):
        print(f"\n✅ Completed validation of all {len(plots)} sequences!")
    
    print("\n📋 VALIDATION SUMMARY:")
    print("   • All plots generated successfully")
    print("   • Review feature accuracy comparison tables")
    print("   • Check for data quality issues highlighted in plots")
    print("   • Verify ColoringFeatureExtractor output matches expectations")

if __name__ == "__main__":
    main()
