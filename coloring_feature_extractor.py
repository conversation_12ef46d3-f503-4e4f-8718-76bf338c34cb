# Import necessary libraries for data processing and analysis
import json
import pandas as pd
import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import warnings
import sys          # For system-specific parameters and functions
import glob         # For file pattern matching
from datetime import datetime  # For timestamps in output files

warnings.filterwarnings('ignore') # Hide warning messages to keep output clean

class ColoringFeatureExtractor:
    """
    A data processing engine that transforms raw touch event data into structured feature sets.

    This class implements a two-stage feature extraction pipeline:
    1. Sequence-level processing: Converts individual touch sequences into 25 numerical features
    2. Session-level aggregation: Combines all sequences into 28 session-wide summary features

    DATA FLOW:
    Raw JSON → Touch sequences → Cleaned events → Kinematic features → Aggregated features → DataFrames
    """

    def __init__(self):
        """
        Initialize the feature extractor with classification thresholds.

        Sets up the numerical thresholds used throughout the feature extraction pipeline
        for categorizing different types of touch interactions and detecting significant
        changes in movement patterns.

        These thresholds are used in conditional logic throughout the codebase to:
        - Classify touch sequences as 'Tap', 'Drag', or 'Hold'
        - Detect significant direction changes in movement paths
        - Filter out noise and stabilize kinematic calculations
        """
        # Time threshold for tap vs hold classification (in seconds)
        # Used in classify_sequence_type() method for conditional logic:
        # if duration < tap_duration_threshold: return 'Tap'
        self.tap_duration_threshold = 0.25

        # Distance threshold for stationary vs movement classification (in pixels)
        # Used in classify_sequence_type() method for conditional logic:
        # if path_length >= tap_distance_threshold: return 'Drag'
        self.tap_distance_threshold = 15

        # Angular threshold for direction change detection (in radians)
        # Used in calculate_sequence_features() to count significant direction changes:
        # if angle_change > direction_change_threshold: direction_changes += 1
        self.direction_change_threshold = np.pi / 6

    def load_json_file(self, filepath: str) -> Dict:
        """
        Load JSON file and extract the nested 'json' data structure.

        Handles the file I/O and data extraction from the specific JSON format used
        by the coloring application. The files have a nested structure where the
        actual touch data is contained within a 'json' key.

        Args:
            filepath (str): Absolute or relative path to the JSON file
                          Example: "raw_data/session_001.json"

        Returns:
            Dict: The extracted data structure, typically containing:
                {
                    'touchData': {
                        '1': [{'x': 100, 'y': 200, 'time': 0.1, ...}, ...],
                        '2': [{'x': 150, 'y': 250, 'time': 1.2, ...}, ...],
                        ...
                    }
                }
                Returns empty dict {} if 'json' key is missing or file is malformed

        IMPLEMENTATION NOTES:
        - Uses context manager (with statement) for automatic file closure
        - Employs defensive programming with .get() to handle missing keys
        - No exception handling - allows FileNotFoundError and JSONDecodeError to propagate
        """
        # Use context manager to ensure file is properly closed after reading
        with open(filepath, 'r') as f:
            # Parse JSON string into Python dictionary object
            data = json.load(f)

        # Extract the nested 'json' key which contains the actual touch data
        # Using .get() with default empty dict prevents KeyError if structure is unexpected
        return data.get('json', {})

    def is_valid_session(self, session_data: Dict) -> bool:
        """
        Validate session data meets minimum requirements for feature extraction.

        Implements data quality checks to filter out sessions with insufficient data
        for reliable feature calculation. Acts as a preprocessing gate before the
        main feature extraction pipeline.

        Args:
            session_data (Dict): Parsed JSON data structure containing:
                {
                    'touchData': {
                        'sequence_id': [list of touch events],
                        ...
                    }
                }

        Returns:
            bool: True if session passes validation checks, False otherwise

        VALIDATION CRITERIA:
        1. Minimum 5 touch sequences (for statistical reliability)
        2. Minimum 10 seconds total duration (for meaningful patterns)

        IMPLEMENTATION DETAILS:
        - Uses defensive programming with .get() to handle missing keys
        - Employs list comprehension for efficient timestamp extraction
        - Calculates duration using min/max operations on flattened timestamp list
        """
        # Extract touchData dictionary, defaulting to empty dict if key missing
        touch_data = session_data.get('touchData', {})

        # Check sequence count: len() operates on dictionary keys
        # Each key represents one touch sequence (continuous finger interaction)
        if len(touch_data) < 5:
            return False  # Insufficient sequences for reliable feature extraction

        # Extract all timestamps from all sequences using nested list comprehension
        # Creates flat list: [time1, time2, time3, ...] from nested structure
        all_times = [event['time'] for sequence in touch_data.values() for event in sequence]

        # Validate duration: calculate total session time span
        # Check for empty list (no events) or insufficient duration
        if not all_times or (max(all_times) - min(all_times)) < 10:
            return False  # Session too short for meaningful analysis

        # Session passes all validation checks
        return True

    def clean_sequence_events(self, events: List[Dict]) -> List[Dict]:
        """
        Remove duplicate timestamps and coordinate outliers from touch event sequence.

        Implements data cleaning pipeline to filter out problematic events that could
        destabilize kinematic calculations. Combines temporal deduplication with
        spatial outlier removal in a single pass through the event list.

        Args:
            events (List[Dict]): List of touch events, each containing:
                {
                    'x': float,      # x-coordinate in pixels
                    'y': float,      # y-coordinate in pixels
                    'time': float,   # timestamp in seconds
                    'zone': str,     # touch zone classification
                    'color': str,    # selected color
                    ...              # additional event properties
                }

        Returns:
            List[Dict]: Filtered list with duplicates and outliers removed
                       Maintains chronological order of remaining events

        CLEANING OPERATIONS:
        1. Temporal deduplication: Remove events with identical timestamps
        2. Spatial filtering: Remove coordinates outside valid screen bounds (>2000px)

        IMPLEMENTATION DETAILS:
        - Single-pass algorithm for efficiency (O(n) time complexity)
        - Uses .get() with defaults for defensive programming
        - Maintains last_timestamp state to detect duplicates
        """
        # Handle empty input case
        if not events:
            return []

        # Initialize output list and state tracking
        cleaned = []
        last_timestamp = None

        # Single pass through events with combined filtering
        for event in events:
            # Spatial outlier filter: reject coordinates beyond reasonable screen bounds
            # 2000px threshold filters out sensor glitches and invalid touch data
            is_valid_coordinate = event.get('x', 0) < 2000 and event.get('y', 0) < 2000

            # Combined temporal and spatial validation
            # Keep event only if: (1) timestamp is unique AND (2) coordinates are valid
            if event['time'] != last_timestamp and is_valid_coordinate:
                cleaned.append(event)
                last_timestamp = event['time']  # Update state for next iteration

        return cleaned

    def calculate_distance(self, p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
        """
        Calculate Euclidean distance between two 2D points using Pythagorean theorem.

        Implements the standard distance formula: d = √[(x₂-x₁)² + (y₂-y₁)²]
        Used throughout the codebase for path length calculations and movement analysis.

        Args:
            p1 (Tuple[float, float]): First point as (x, y) coordinates in pixels
            p2 (Tuple[float, float]): Second point as (x, y) coordinates in pixels

        Returns:
            float: Distance in pixels (always non-negative)

        MATHEMATICAL IMPLEMENTATION:
        - Computes coordinate differences: Δx = x₂ - x₁, Δy = y₂ - y₁
        - Applies Pythagorean theorem: distance = √(Δx² + Δy²)
        - Uses numpy.sqrt for numerical stability and performance
        """
        # Calculate coordinate differences
        dx = p2[0] - p1[0]  # Δx = x₂ - x₁
        dy = p2[1] - p1[1]  # Δy = y₂ - y₁

        # Apply Pythagorean theorem: √(Δx² + Δy²)
        return np.sqrt(dx**2 + dy**2)

    def calculate_angle_change(self, p1, p2, p3) -> float:
        """
        Calculate the angle between two consecutive line segments using vector dot product.

        Computes the angle change at point p2 when moving from p1→p2→p3.
        Uses vector mathematics to determine how much the movement direction changed.

        Args:
            p1: First point (x, y) - start of first segment
            p2: Middle point (x, y) - vertex where angle is measured
            p3: Third point (x, y) - end of second segment

        Returns:
            float: Angle change in radians [0, π]
                  0 = no direction change (straight line)
                  π = complete reversal (180° turn)

        VECTOR ALGORITHM:
        1. Create direction vectors: v1 = p2-p1, v2 = p3-p2
        2. Calculate vector magnitudes (lengths)
        3. Compute dot product: v1·v2 = |v1||v2|cos(θ)
        4. Solve for angle: θ = arccos((v1·v2)/(|v1||v2|))

        EDGE CASE HANDLING:
        - Returns 0 if either vector has zero length (no movement)
        - Uses np.clip to ensure arccos input stays in valid range [-1, 1]
        """
        # Create direction vectors from consecutive point pairs
        v1 = (p2[0] - p1[0], p2[1] - p1[1])  # Vector from p1 to p2
        v2 = (p3[0] - p2[0], p3[1] - p2[1])  # Vector from p2 to p3

        # Calculate vector magnitudes (lengths) using Euclidean norm
        mag1 = np.sqrt(v1[0]**2 + v1[1]**2)  # |v1| = √(vx² + vy²)
        mag2 = np.sqrt(v2[0]**2 + v2[1]**2)  # |v2| = √(vx² + vy²)

        # Handle zero-length vectors (no movement between points)
        if mag1 == 0 or mag2 == 0:
            return 0  # No angle change when there's no movement

        # Calculate dot product: v1·v2 = v1x*v2x + v1y*v2y
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]

        # Apply dot product formula: cos(θ) = (v1·v2) / (|v1||v2|)
        cos_angle = dot_product / (mag1 * mag2)

        # Clamp to valid arccos domain [-1, 1] to prevent numerical errors
        cos_angle = np.clip(cos_angle, -1.0, 1.0)

        # Convert from cosine back to angle in radians
        return np.arccos(cos_angle)

    def classify_sequence_type(self, duration: float, path_length: float) -> str:
        """
        Classify touch sequence into one of three interaction types using rule-based logic.

        Implements a hierarchical classification system that first checks movement distance,
        then uses duration to distinguish between stationary interaction types.

        Args:
            duration (float): Total time span of the sequence in seconds
            path_length (float): Total distance traveled in pixels

        Returns:
            str: One of three classification labels:
                'Drag' - Movement-based interaction (drawing, painting)
                'Tap'  - Brief stationary interaction (quick touch)
                'Hold' - Extended stationary interaction (sustained touch)

        CLASSIFICATION ALGORITHM:
        1. Primary check: If path_length >= 15px → 'Drag' (movement detected)
        2. Secondary check: If duration < 0.25s → 'Tap' (brief stationary)
        3. Default case: → 'Hold' (extended stationary)

        IMPLEMENTATION RATIONALE:
        - Movement takes precedence over duration (drag classification is primary)
        - Duration threshold only applies to stationary interactions
        - Uses instance thresholds for consistency across the codebase
        """
        # Primary classification: Check for movement-based interaction
        # If significant movement detected, classify as 'Drag' regardless of duration
        if path_length >= self.tap_distance_threshold:
            return 'Drag'  # Movement indicates drawing/painting behavior

        # Secondary classification: For stationary interactions, use duration
        # Brief stationary interactions are classified as 'Tap'
        elif duration < self.tap_duration_threshold:
            return 'Tap'   # Quick stationary touch

        # Default case: Extended stationary interactions are classified as 'Hold'
        else:
            return 'Hold'  # Sustained stationary touch

    def _calculate_kinematics(self, events: List[Dict]) -> pd.DataFrame:
        """
        Takes raw events and returns a clean DataFrame with smoothed coordinates and robust kinematic features.
        """
        # Return an empty DataFrame if not enough events
        if len(events) < 2:
            return pd.DataFrame()

        # --- Step 1: Initial Cleaning ---
        # Remove outlier coordinates and duplicate timestamps
        cleaned_events = []
        last_timestamp = None
        for e in events:
            if e['time'] != last_timestamp and e['x'] < 2000 and e['y'] < 2000:
                cleaned_events.append(e)
                last_timestamp = e['time']

        if len(cleaned_events) < 2:
            return pd.DataFrame()

        df = pd.DataFrame(cleaned_events)

        # --- Step 2: Smoothing ---
        df['x'] = df['x'].rolling(window=3, min_periods=1, center=True).mean()
        df['y'] = df['y'].rolling(window=3, min_periods=1, center=True).mean()

        # --- Step 3: Robust Derivative Calculation ---
        MIN_TIME_DELTA = 0.001  # 1 millisecond threshold

        df['delta_time'] = df['time'].diff()
        delta_d = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)

        # Proactively prevent division by unstable time intervals
        velocity_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA, delta_d / df['delta_time'], 0)
        df['Velocity'] = pd.Series(velocity_calc, index=df.index).fillna(0)

        delta_v = df['Velocity'].diff()
        acceleration_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA, delta_v / df['delta_time'], 0)
        df['Acceleration'] = pd.Series(acceleration_calc, index=df.index).fillna(0)

        delta_a = df['Acceleration'].diff()
        jerk_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA, delta_a / df['delta_time'], 0)
        df['Jerk'] = pd.Series(jerk_calc, index=df.index).fillna(0)

        return df

    def calculate_sequence_features(self, events: List[Dict], sequence_id: int) -> Dict:
        """
        Extract 25 numerical features from a single touch sequence using stabilized kinematic pipeline.

        Implements a comprehensive feature extraction algorithm that transforms raw touch events
        into a structured feature vector suitable for machine learning or statistical analysis.
        Combines basic temporal/spatial features with advanced kinematic analysis.

        Args:
            events (List[Dict]): Chronologically ordered touch events containing:
                [
                    {
                        'x': float,        # x-coordinate in pixels
                        'y': float,        # y-coordinate in pixels
                        'time': float,     # timestamp in seconds
                        'zone': str,       # spatial zone classification
                        'color': str,      # selected color
                        'accx': float,     # accelerometer x-axis
                        'accy': float,     # accelerometer y-axis
                        'accz': float,     # accelerometer z-axis
                        ...
                    },
                    ...
                ]
            sequence_id (int): Unique identifier for this touch sequence

        Returns:
            Dict: Feature dictionary with 25 numerical features:
                {
                    'sequence_id': int,           # Identifier
                    'sequence_type': str,         # 'Tap'/'Drag'/'Hold'
                    'duration': float,            # Total time span
                    'path_length': float,         # Total distance traveled
                    'velocity_mean': float,       # Average movement speed
                    'acc_mean': float,           # Average acceleration
                    'jerk_mean': float,          # Average jerk
                    'drag_area': float,          # Bounding box area
                    'direction_changes': int,     # Number of direction changes
                    'error_rate': float,         # Proportion outside valid zones
                    ...                          # Additional 15 features
                }
            Returns None if insufficient events (< 2) for feature calculation

        ALGORITHM OVERVIEW:
        1. Basic feature calculation from raw events (duration, path_length, classification)
        2. DataFrame creation and coordinate smoothing (3-point rolling window)
        3. Kinematic feature calculation (velocity, acceleration, jerk) with time thresholds
        4. Outlier clipping using quantile-based bounds (1st/99th percentiles)
        5. Statistical aggregation and additional feature computation
        6. Feature dictionary compilation and return

        STABILIZATION TECHNIQUES:
        - Coordinate smoothing: Reduces sensor noise using rolling window
        - Time thresholds: Prevents division by near-zero time intervals (1ms minimum)
        - Quantile clipping: Removes extreme kinematic outliers for stability
        """
        # Input validation: require minimum 2 events for meaningful feature calculation
        if len(events) < 2:
            return None  # Cannot calculate features from single event

        # === STEP 1: BASIC FEATURE CALCULATION ===
        # Calculate fundamental temporal and spatial features from raw event data

        # Duration: total time span from first to last event
        duration = events[-1]['time'] - events[0]['time']

        # Path length: sum of Euclidean distances between consecutive points
        # Uses generator expression for memory efficiency with large sequences
        path_length = sum(self.calculate_distance((events[i-1]['x'], events[i-1]['y']),
                                                  (events[i]['x'], events[i]['y']))
                         for i in range(1, len(events)))

        # Sequence classification using rule-based algorithm
        sequence_type = self.classify_sequence_type(duration, path_length)

        # Extract starting color for session-level color change analysis
        start_color = events[0]['color']

        # === STEP 2: DATAFRAME CREATION AND COORDINATE SMOOTHING ===
        # Convert event list to pandas DataFrame for vectorized operations
        df = pd.DataFrame(events)

        # STABILIZATION TECHNIQUE 1: Coordinate Smoothing
        # Apply 3-point rolling window to reduce sensor noise and movement jitter
        # center=True ensures symmetric smoothing, min_periods=1 handles edge cases
        df['x'] = df['x'].rolling(window=3, min_periods=1, center=True).mean()
        df['y'] = df['y'].rolling(window=3, min_periods=1, center=True).mean()

        # === STEP 3: KINEMATIC FEATURE CALCULATION WITH STABILIZATION ===

        # STABILIZATION TECHNIQUE 2: Time Threshold Filtering
        # Set minimum time delta to prevent division by near-zero values
        # 1ms threshold eliminates most numerical instabilities from rapid sampling
        MIN_TIME_DELTA = 0.001  # 1 millisecond minimum time interval

        # Calculate time differences between consecutive events
        df['delta_time'] = df['time'].diff()

        # Calculate spatial displacement between consecutive smoothed coordinates
        delta_d = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)

        # VELOCITY CALCULATION: distance/time with time threshold protection
        # Use np.where to conditionally calculate only when time delta is sufficient
        velocity_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA,
                                delta_d / df['delta_time'], 0)
        # Convert numpy array to pandas Series for proper DataFrame integration
        df['Velocity'] = pd.Series(velocity_calc, index=df.index).fillna(0)

        # ACCELERATION CALCULATION: velocity_change/time with time threshold protection
        delta_v = df['Velocity'].diff()
        acceleration_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA,
                                    delta_v / df['delta_time'], 0)
        df['Acceleration'] = pd.Series(acceleration_calc, index=df.index).fillna(0)

        # JERK CALCULATION: acceleration_change/time with time threshold protection
        delta_a = df['Acceleration'].diff()
        jerk_calc = np.where(df['delta_time'] >= MIN_TIME_DELTA,
                            delta_a / df['delta_time'], 0)
        df['Jerk'] = pd.Series(jerk_calc, index=df.index).fillna(0)

        # === STEP 4: OUTLIER CLIPPING FOR KINEMATIC STABILITY ===

        # STABILIZATION TECHNIQUE 3: Quantile-based Outlier Clipping
        # Calculate 1st and 99th percentiles to define outlier bounds
        # This removes extreme values while preserving the bulk of the data distribution
        accel_lower, accel_upper = df['Acceleration'].quantile([0.01, 0.99])
        jerk_lower, jerk_upper = df['Jerk'].quantile([0.01, 0.99])

        # Apply clipping to create stabilized versions of kinematic features
        # .clip() method bounds values to the specified range
        df['acc_clipped'] = df['Acceleration'].clip(lower=accel_lower, upper=accel_upper)
        df['jerk_clipped'] = df['Jerk'].clip(lower=jerk_lower, upper=jerk_upper)

        # === STEP 5: STATISTICAL AGGREGATION OF KINEMATIC FEATURES ===

        # Calculate central tendency measures for velocity (using raw values)
        velocity_mean = df['Velocity'].mean()      # Average movement speed
        velocity_median = df['Velocity'].median()  # Median movement speed (robust to outliers)

        # Calculate central tendency measures for acceleration (using clipped values for stability)
        acc_mean = df['acc_clipped'].mean()        # Average acceleration magnitude
        acc_median = df['acc_clipped'].median()    # Median acceleration magnitude

        # Calculate central tendency measures for jerk (using clipped values for stability)
        jerk_mean = df['jerk_clipped'].mean()      # Average jerk magnitude
        jerk_median = df['jerk_clipped'].median()  # Median jerk magnitude

        # === STEP 6: SPATIAL AND GEOMETRIC FEATURE CALCULATION ===

        # Extract start and end coordinates from original (unsmoothed) events
        start_point = (events[0]['x'], events[0]['y'])
        end_point = (events[-1]['x'], events[-1]['y'])

        # Calculate bounding box area using smoothed coordinates
        # Represents the total spatial extent of the movement
        width = df['x'].max() - df['x'].min()
        height = df['y'].max() - df['y'].min()
        drag_area = width * height

        # Calculate movement efficiency metrics
        direct_distance = self.calculate_distance(start_point, end_point)
        # Path straightness: ratio of direct distance to actual path length
        # Values close to 1.0 indicate straight-line movement
        path_straightness = direct_distance / path_length if path_length > 0 else 1.0

        # Calculate average speed using total path length and duration
        avg_speed = path_length / duration if duration > 0 else 0

        # Calculate speed variability (standard deviation of instantaneous speeds)
        # Filter out zero time deltas to avoid including stationary moments
        speeds = df[df['delta_time'].fillna(0) > 0]['Velocity'].tolist()
        speed_variability = np.std(speeds) if speeds else 0

        # === STEP 7: ACCELEROMETER-BASED FEATURES ===

        # Calculate 3D accelerometer magnitude for each event
        # Uses corrected 'accz' field (fixed typo from original 'acz')
        acc_magnitudes = [np.sqrt(e['accx']**2 + e['accy']**2 + e['accz']**2)
                         for e in events]
        acc_magnitude_mean = np.mean(acc_magnitudes)  # Average 3D acceleration magnitude
        acc_magnitude_std = np.std(acc_magnitudes)    # Variability in 3D acceleration

        # === STEP 8: MOVEMENT QUALITY ANALYSIS ===

        # Count significant direction changes using three-point angle analysis
        direction_changes = 0
        if len(events) >= 3:  # Need minimum 3 points to calculate angles
            # Iterate through interior points (excluding first and last)
            for i in range(1, len(events) - 1):
                # Extract three consecutive points for angle calculation
                p1 = (events[i-1]['x'], events[i-1]['y'])  # Previous point
                p2 = (events[i]['x'], events[i]['y'])      # Current point (vertex)
                p3 = (events[i+1]['x'], events[i+1]['y'])  # Next point

                # Calculate angle change and compare to threshold
                angle_change = self.calculate_angle_change(p1, p2, p3)
                if angle_change > self.direction_change_threshold:
                    direction_changes += 1  # Count as significant direction change

        # === STEP 9: SPATIAL ZONE ANALYSIS ===

        # Extract zone classifications for all events
        zones = [e['zone'] for e in events]

        # Identify primary zone (first zone encountered)
        primary_zone = zones[0]

        # Calculate proportion of time spent in primary zone
        proportion_in_primary_zone = zones.count(primary_zone) / len(zones)

        # Count zone boundary crossings (transitions between different zones)
        zone_crossings = sum(1 for i in range(1, len(zones))
                           if zones[i] != zones[i-1])

        # Calculate error rate (proportion of touches outside valid coloring areas)
        error_zones = ['Outside', 'Super outside']
        error_rate = sum(1 for z in zones if z in error_zones) / len(zones)

        # Determine zone consistency (1 if stayed in same zone, 0 if moved between zones)
        zone_consistency = 1 if len(set(zones)) == 1 else 0

        # === STEP 10: FEATURE DICTIONARY COMPILATION ===

        # Compile all calculated features into structured dictionary
        # Organized by feature category for clarity and maintainability
        return {
            # IDENTIFICATION FEATURES
            'sequence_id': sequence_id,                    # Unique sequence identifier
            'sequence_type': sequence_type,                # Classification: 'Tap'/'Drag'/'Hold'
            'start_color': start_color,                    # Color selected at sequence start

            # TEMPORAL FEATURES
            'start_time': events[0]['time'],               # Sequence start timestamp
            'end_time': events[-1]['time'],                # Sequence end timestamp
            'duration': duration,                          # Total time span (seconds)

            # SPATIAL FEATURES
            'path_length': path_length,                    # Total distance traveled (pixels)
            'path_straightness': path_straightness,        # Movement efficiency ratio [0,1]
            'drag_area': drag_area,                        # Bounding box area (pixels²)
            'direction_changes': direction_changes,        # Count of significant direction changes

            # KINEMATIC FEATURES (VELOCITY)
            'velocity_mean': velocity_mean,                # Average movement speed (pixels/sec)
            'velocity_median': velocity_median,            # Median movement speed (pixels/sec)
            'avg_speed': avg_speed,                        # Overall average speed (pixels/sec)
            'speed_variability': speed_variability,        # Speed standard deviation

            # KINEMATIC FEATURES (ACCELERATION)
            'acc_mean': acc_mean,                          # Average acceleration (clipped)
            'acc_median': acc_median,                      # Median acceleration (clipped)

            # KINEMATIC FEATURES (JERK)
            'jerk_mean': jerk_mean,                        # Average jerk (clipped)
            'jerk_median': jerk_median,                    # Median jerk (clipped)

            # ACCELEROMETER FEATURES
            'acc_magnitude_mean': acc_magnitude_mean,      # Average 3D acceleration magnitude
            'acc_magnitude_std': acc_magnitude_std,        # 3D acceleration variability

            # SPATIAL ZONE FEATURES
            'primary_zone': primary_zone,                  # First zone encountered
            'proportion_in_primary_zone': proportion_in_primary_zone,  # Time in primary zone
            'zone_crossings': zone_crossings,              # Number of zone transitions
            'zone_consistency': zone_consistency,          # Binary: stayed in same zone
            'error_rate': error_rate                       # Proportion outside valid zones
        }



    def calculate_session_features(self, all_sequence_features: List[Dict], all_events: List[Dict], filename: str) -> Dict:
        """
        Create a comprehensive behavioral profile for an entire coloring session.

        Think of this as creating a "behavioral report card" that summarizes how
        someone colored during their entire session. While individual sequences
        tell us about specific finger movements, this method looks at the big
        picture patterns across all their interactions.

        This analysis reveals:
        - Overall engagement patterns (how long they worked, how many breaks)
        - Behavioral consistency (do they have a preferred interaction style?)
        - Task performance (how much they completed, how efficiently)
        - Motor skill patterns (are their movements generally smooth or erratic?)
        - Attention and focus indicators (repetitive behaviors, pause patterns)

        Args:
            all_sequence_features: List of feature dictionaries from all sequences
            all_events: List of all individual touch events (for timing analysis)
            filename: Name of the session file (for identification)

        Returns:
            Dictionary with 28 session-level behavioral features

        Feature Categories:
        1. Temporal Patterns: timing, pauses, session duration
        2. Behavioral Patterns: sequence types, transitions, repetition
        3. Performance Metrics: completion, efficiency, preferences
        4. Motor Control Summary: movement quality across all interactions
        """

        # === TEMPORAL PATTERN ANALYSIS ===
        # Analyze timing patterns across the entire session

        # Calculate total session duration (first touch to last touch)
        total_time_spent = all_events[-1]['time'] - all_events[0]['time'] if all_events else 0

        # Calculate average duration of individual sequences
        # This tells us their typical interaction length (quick taps vs. long drags)
        avg_sequence_duration = np.mean([f['duration'] for f in all_sequence_features])

        # === PAUSE PATTERN ANALYSIS ===
        # Analyze the gaps between sequences to understand attention and planning patterns

        # Calculate time gaps between consecutive sequences
        time_between_sequences = [all_sequence_features[i]['start_time'] - all_sequence_features[i-1]['end_time']
                                for i in range(1, len(all_sequence_features))]

        # Calculate average pause time (excluding negative values from overlapping sequences)
        avg_time_between_sequences = np.mean([t for t in time_between_sequences if t > 0]) if any(t > 0 for t in time_between_sequences) else 0

        # Find the longest single pause (might indicate distraction, planning, or difficulty)
        longest_pause = max(time_between_sequences) if time_between_sequences else 0

        # === KINEMATIC AGGREGATION ANALYSIS ===
        # Calculate session-wide averages of the new kinematic features

        # Calculate aggregate kinematics across all sequences
        avg_velocity_mean = np.mean([f['velocity_mean'] for f in all_sequence_features])
        avg_acc_mean = np.mean([f['acc_mean'] for f in all_sequence_features])
        avg_jerk_mean = np.mean([f['jerk_mean'] for f in all_sequence_features])

        # Calculate total area covered by all sequences
        total_area = sum(f['drag_area'] for f in all_sequence_features)

        # Identify shortest and longest drag sequences
        drag_features = [f for f in all_sequence_features if f['sequence_type'] == 'Drag']

        # Find shortest and longest drags by path length
        shortest_drag_id = None
        longest_drag_id = None

        if drag_features:
            # Find the drag with minimum path length
            shortest_drag = min(drag_features, key=lambda x: x['path_length'])
            shortest_drag_id = shortest_drag['sequence_id']

            # Find the drag with maximum path length
            longest_drag = max(drag_features, key=lambda x: x['path_length'])
            longest_drag_id = longest_drag['sequence_id']

        # === BEHAVIORAL PATTERN ANALYSIS ===
        # Analyze interaction patterns to understand behavioral consistency and preferences

        # Extract sequence types to analyze interaction strategy
        sequence_types = [f['sequence_type'] for f in all_sequence_features]

        # Count how often they switch between different interaction types (Tap→Drag→Hold)
        # High transition rates might indicate:
        # - Exploratory behavior (trying different approaches)
        # - Inconsistent motor planning
        # - Adaptive strategy (switching based on task demands)
        type_transitions = sum(1 for i in range(1, len(sequence_types)) if sequence_types[i] != sequence_types[i-1])

        # === PREFERENCE ANALYSIS ===
        # Identify preferred colors and zones to understand behavioral patterns

        # Analyze color usage patterns
        colors = [event['color'] for event in all_events]
        most_frequent_color = max(set(colors), key=colors.count) if colors else 'Unknown'

        # Analyze spatial preferences (which areas they focus on)
        zones = [event['zone'] for event in all_events]
        most_frequent_zone = max(set(zones), key=zones.count) if zones else 'Unknown'

        # FIX: Simplified and corrected color switch and repetitive action loop
        color_switches = 0
        repetitive_actions = 0
        for i in range(1, len(all_sequence_features)):
            if all_sequence_features[i-1]['start_color'] != all_sequence_features[i]['start_color']:
                color_switches += 1
            time_diff = all_sequence_features[i]['start_time'] - all_sequence_features[i-1]['start_time']
            if (time_diff <= 2.0 and all_sequence_features[i]['primary_zone'] == all_sequence_features[i-1]['primary_zone']):
                repetitive_actions += 1
        # === TASK COMPLETION ANALYSIS ===
        # Analyze how much of the coloring task was completed and how efficiently

        # Calculate final completion percentage
        final_completion = all_events[-1]['completionPerc'] if all_events else 0

        # Calculate completion efficiency (progress per unit time)
        # Higher values indicate more efficient coloring (more progress in less time)
        completion_efficiency = (final_completion - all_events[0]['completionPerc']) / total_time_spent if total_time_spent > 0 else 0

        # === DRAG-SPECIFIC MOTOR CONTROL ANALYSIS ===
        # Analyze drawing/painting movements for motor control quality

        # Filter out only drag sequences for specialized analysis
        drag_features = [f for f in all_sequence_features if f['sequence_type'] == 'Drag']

        # Calculate average characteristics of drag movements
        avg_path_length_drag = np.mean([f['path_length'] for f in drag_features]) if drag_features else 0
        avg_straightness_drag = np.mean([f['path_straightness'] for f in drag_features]) if drag_features else 0

        # === ERRATIC MOVEMENT DETECTION ===
        # Identify drags that show poor motor control (erratic movements)

        # Get speed variability and direction changes for drags that show these behaviors
        speed_vars = [f['speed_variability'] for f in drag_features if f['speed_variability'] > 0]
        dir_changes = [f['direction_changes'] for f in drag_features if f['direction_changes'] > 0]

        # Use 75th percentile as threshold for "erratic" behavior
        # This means the top 25% most variable movements are considered erratic
        speed_threshold = np.percentile(speed_vars, 75) if speed_vars else 0
        direction_threshold = np.percentile(dir_changes, 75) if dir_changes else 0

        # Count drags that exceed either threshold (high speed variation OR many direction changes)
        erratic_drags_count = sum(1 for f in drag_features if f['speed_variability'] > speed_threshold or f['direction_changes'] > direction_threshold)

        # === TAP-SPECIFIC PRECISION ANALYSIS ===
        # Analyze quick touch interactions for targeting accuracy and precision

        # Filter out only tap sequences for specialized analysis
        tap_features = [f for f in all_sequence_features if f['sequence_type'] == 'Tap']

        # Calculate average duration of tap sequences
        # Very brief taps might indicate good motor control, while longer "taps" might indicate hesitation
        avg_tap_duration = np.mean([f['duration'] for f in tap_features]) if tap_features else 0

        # Calculate tap accuracy (how many taps landed in valid coloring areas)
        valid_taps = sum(1 for f in tap_features if f['primary_zone'] not in ['Outside', 'Super outside'])
        proportion_taps_in_valid_zones = valid_taps / len(tap_features) if tap_features else 0

        # === HOLD-SPECIFIC ATTENTION ANALYSIS ===
        # Analyze sustained touch interactions for attention and motor planning

        # Filter out only hold sequences for specialized analysis
        hold_features = [f for f in all_sequence_features if f['sequence_type'] == 'Hold']

        # Calculate average duration of hold sequences
        # Longer holds might indicate careful planning or difficulty with motor execution
        avg_hold_duration = np.mean([f['duration'] for f in hold_features]) if hold_features else 0

        # Count total number of hold sequences
        hold_count = len(hold_features)

        # === COMPILE COMPREHENSIVE SESSION PROFILE ===
        # Package all session-level behavioral features into a complete behavioral profile
        return {
            # === IDENTIFICATION ===
            'filename': filename,  # Session identifier

            # === TEMPORAL PATTERNS ===
            'total_time_spent': total_time_spent,                    # Total session duration (seconds)
            'total_sequences': len(all_sequence_features),           # Total number of interactions
            'avg_sequence_duration': avg_sequence_duration,          # Average interaction length (seconds)
            'avg_time_between_sequences': avg_time_between_sequences, # Average pause between interactions (seconds)
            'longest_pause': longest_pause,                         # Longest single pause (seconds)

            # === KINEMATIC AGGREGATIONS ===
            'avg_velocity_mean': avg_velocity_mean,                  # Session-wide average velocity (pixels/second)
            'avg_acc_mean': avg_acc_mean,                           # Session-wide average acceleration (pixels/second²)
            'avg_jerk_mean': avg_jerk_mean,                         # Session-wide average jerk (pixels/second³)
            'total_area': total_area,                               # Total area covered by all sequences (pixels²)
            'shortest_drag_id': shortest_drag_id,                   # Sequence ID of shortest drag by path length
            'longest_drag_id': longest_drag_id,                     # Sequence ID of longest drag by path length

            # === BEHAVIORAL PATTERNS ===
            'sequence_type_transition_rate': type_transitions / len(all_sequence_features) if len(all_sequence_features) > 1 else 0,  # How often they switch interaction types
            'color_switch_frequency': color_switches,               # Number of color changes
            'repetitive_actions': repetitive_actions,               # Number of repetitive behaviors

            # === PREFERENCES AND HABITS ===
            'most_frequent_zone': most_frequent_zone,               # Their preferred coloring area
            'most_frequent_color': most_frequent_color,             # Their preferred color

            # === TASK PERFORMANCE ===
            'final_completion_percentage': final_completion,        # How much of the task they completed
            'completion_efficiency': completion_efficiency,         # How efficiently they worked (progress/time)

            # === INTERACTION TYPE COUNTS ===
            'drag_count': len(drag_features),                       # Number of drawing/painting movements
            'tap_count': len(tap_features),                         # Number of quick touches
            'hold_count': hold_count,                               # Number of sustained touches

            # === MOTOR CONTROL QUALITY ===
            'average_path_length_drag': avg_path_length_drag,       # Average distance of drawing movements
            'average_straightness_drag': avg_straightness_drag,     # How straight their drawing movements were
            'erratic_drags_count': erratic_drags_count,             # Number of erratic/shaky drawing movements

            # === PRECISION AND ATTENTION ===
            'average_tap_duration': avg_tap_duration,               # How long their quick touches lasted
            'proportion_of_taps_in_valid_zones': proportion_taps_in_valid_zones,  # Accuracy of quick touches
            'average_hold_duration': avg_hold_duration              # How long their sustained touches lasted
        }



    def process_directory(self, directory_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Process all coloring session files in a directory to create comprehensive behavioral datasets.

        Think of this as a batch processing factory that takes a folder full of
        coloring session files and transforms them into two types of behavioral
        datasets that researchers and practitioners can use for analysis.

        This method is the main entry point for large-scale behavioral analysis.
        It handles file management, error recovery, data validation, and creates
        clean datasets ready for statistical analysis, machine learning, or
        clinical assessment.

        The two output datasets serve different purposes:
        1. SESSION DATASET: One row per person/session - for comparing individuals
        2. INTERACTION DATASET: One row per finger movement - for detailed analysis

        Args:
            directory_path: Path to folder containing JSON coloring session files

        Returns:
            Tuple of (session_dataframe, interaction_dataframe):
            - session_dataframe: 28 behavioral features per session (person-level analysis)
            - interaction_dataframe: 25 features per touch sequence (movement-level analysis)

        Use Cases:
        - Research: Compare behavioral patterns across different populations
        - Clinical: Track individual progress over time
        - Educational: Assess fine motor skill development
        - Technology: Improve app design based on user behavior patterns
        """
        # === DIRECTORY SETUP AND VALIDATION ===
        # Convert string path to Path object for robust file handling
        directory = Path(directory_path)

        # Find all JSON files in the specified directory
        json_files = list(directory.glob("*.json"))  # *.json = any file ending in .json

        # Check if we found any files to process
        if not json_files:
            print(f"No JSON files found in {directory_path}")
            return pd.DataFrame(), pd.DataFrame()  # Return empty datasets

        # === INITIALIZE DATA COLLECTION ===
        # Lists to store extracted features from all sessions
        all_session_summaries = []    # One summary per session file
        all_interaction_details = []  # All individual touch sequences from all sessions

        # Counters to track processing progress and quality
        processed_count, skipped_count = 0, 0

        # === PROCESS EACH SESSION FILE ===
        # Iterate through all JSON files and extract behavioral features
        for json_file in json_files:
            try:
                # === LOAD AND VALIDATE SESSION DATA ===
                # Load the JSON file and extract touch interaction data
                session_data = self.load_json_file(str(json_file))

                # Check if this session meets our quality standards
                if not self.is_valid_session(session_data):
                    skipped_count += 1
                    print(f"Skipped invalid session: {json_file.name}")
                    continue  # Skip to next file

                # === EXTRACT TOUCH SEQUENCES ===
                # Get all touch sequences from this session
                touch_data = session_data.get('touchData', {})

                # Process each individual touch sequence in this session
                sequences_features = []  # Features for each sequence in this session
                all_events = []         # All raw touch events from this session

                # Process sequences in chronological order
                for seq_id in sorted(touch_data.keys(), key=int):
                    # Clean up the touch events in this sequence (remove duplicates)
                    cleaned_events = self.clean_sequence_events(touch_data[seq_id])

                    # Only process sequences with valid data
                    if cleaned_events:
                        # Extract behavioral features from this sequence
                        features = self.calculate_sequence_features(cleaned_events, int(seq_id))

                        # Only keep sequences that produced valid features
                        if features:
                            # Add filename to link this sequence back to its session
                            features['filename'] = json_file.name
                            sequences_features.append(features)

                            # Keep all raw events for session-level timing analysis
                            all_events.extend(cleaned_events)

                # === VALIDATE SEQUENCE PROCESSING ===
                # Check if we got any valid sequences from this session
                if not sequences_features:
                    skipped_count += 1
                    print(f"Skipped session with no valid sequences after cleaning: {json_file.name}")
                    continue  # Skip to next file

                # === CREATE SESSION SUMMARY ===
                # Sort all events chronologically for accurate timing analysis
                all_events.sort(key=lambda x: x['time'])

                # Create comprehensive session-level behavioral profile
                session_summary = self.calculate_session_features(sequences_features, all_events, json_file.name)

                # === STORE RESULTS ===
                # Add this session's data to our master datasets
                all_session_summaries.append(session_summary)      # Session-level features
                all_interaction_details.extend(sequences_features) # Sequence-level features
                processed_count += 1

            except Exception as e:
                # Handle any errors that occur during processing
                print(f"Error processing {json_file.name}: {e}")
                skipped_count += 1
                # Continue processing other files even if one fails

        # === PROCESSING SUMMARY ===
        # Report the results of our batch processing
        print(f"\nProcessed {processed_count} valid sessions, skipped {skipped_count} invalid sessions.")

        # === CREATE FINAL DATASETS ===
        # Convert our lists of feature dictionaries into pandas DataFrames (tables)
        # These DataFrames are ready for statistical analysis, visualization, or export

        # Session-level dataset: One row per coloring session
        session_df = pd.DataFrame(all_session_summaries)

        # Interaction-level dataset: One row per touch sequence
        interaction_df = pd.DataFrame(all_interaction_details)

        # === FINAL DATA CLEANING ===
        # Clean up any problematic values that might interfere with analysis
        for df in [session_df, interaction_df]:
            if not df.empty:
                # Replace infinity values with NaN (Not a Number)
                # Infinity can occur from division by zero in edge cases
                df.replace([np.inf, -np.inf], np.nan, inplace=True)

                # Replace NaN values with 0 for consistent analysis
                # This ensures all features have numeric values
                df.fillna(0, inplace=True)

        # === RETURN BEHAVIORAL DATASETS ===
        # Return both datasets for different types of analysis:
        # - session_df: For comparing individuals or tracking progress over time
        # - interaction_df: For detailed movement analysis or machine learning
        return session_df, interaction_df
    
































































#-------------------------------------------------------------------------------------------------------------------------------==============
#---------------------------------------------------
#---------------------------------------------------
#---------------------------------------------------
#---------------------------------------------------(unility, not realted to feature derivation)
class ColoringFeatureExtractorCLI:  #for command line and ease of use only.  
    """
    Interactive Command Line Interface for the Coloring Feature Extractor.

    This class provides a user-friendly way to run feature extraction without
    manually editing code. It handles file selection, output options, and
    provides progress feedback.
    """

    def __init__(self):
        """Initialize the CLI with default settings."""
        self.extractor = ColoringFeatureExtractor()
        self.default_data_dir = Path("raw_data")  # Default directory for JSON files
        self.default_output_dir = Path(".")       # Default output directory (current folder)

    def display_banner(self):
        """Display a welcome banner for the CLI."""
        print("=" * 60)
        print("🎨 COLORING FEATURE EXTRACTOR CLI 🎨")
        print("=" * 60)
        print("Extract behavioral features from coloring session data")
        print("=" * 60)
        print()

    def get_user_choice(self, prompt: str, options: List[str], allow_custom: bool = False) -> str:
        """
        Get user choice from a list of options.

        Args:
            prompt: Question to ask the user
            options: List of valid options
            allow_custom: Whether to allow custom input beyond the options

        Returns:
            User's choice (validated)
        """
        while True:
            print(prompt)
            for i, option in enumerate(options, 1):
                print(f"  {i}. {option}")

            if allow_custom:
                print(f"  {len(options) + 1}. Enter custom path")

            try:
                choice = input("\nEnter your choice (number): ").strip()
                choice_num = int(choice)

                if 1 <= choice_num <= len(options):
                    return options[choice_num - 1]
                elif allow_custom and choice_num == len(options) + 1:
                    return "CUSTOM"
                else:
                    print(f"❌ Please enter a number between 1 and {len(options) + (1 if allow_custom else 0)}")

            except ValueError:
                print("❌ Please enter a valid number")
            print()

    def get_custom_path(self, prompt: str, must_exist: bool = True) -> Path:
        """
        Get a custom file or directory path from the user.

        Args:
            prompt: Question to ask the user
            must_exist: Whether the path must already exist

        Returns:
            Valid Path object
        """
        while True:
            path_str = input(f"{prompt}: ").strip()
            if not path_str:
                print("❌ Please enter a path")
                continue

            path = Path(path_str)

            if must_exist and not path.exists():
                print(f"❌ Path does not exist: {path}")
                continue

            return path

    def select_input_files(self) -> List[Path]:
        """
        Let user select which files to process.

        Returns:
            List of JSON file paths to process
        """
        print("\n📁 SELECT INPUT FILES")
        print("-" * 30)

        # Check if default data directory exists
        if self.default_data_dir.exists():
            json_files_count = len(list(self.default_data_dir.glob("*.json")))
            default_option = f"Process all files in '{self.default_data_dir}' ({json_files_count} JSON files)"
        else:
            default_option = f"Process all files in '{self.default_data_dir}' (directory not found)"

        options = [
            default_option,
            "Select specific files from raw_data",
            "Choose a different directory",
            "Select individual files from anywhere"
        ]

        choice = self.get_user_choice("How would you like to select input files?", options)

        if choice == options[0]:  # All files in raw_data
            if not self.default_data_dir.exists():
                print(f"❌ Directory '{self.default_data_dir}' does not exist!")
                return self.select_input_files()  # Try again

            json_files = list(self.default_data_dir.glob("*.json"))
            if not json_files:
                print(f"❌ No JSON files found in '{self.default_data_dir}'!")
                return self.select_input_files()  # Try again

            print(f"✅ Selected {len(json_files)} files from '{self.default_data_dir}'")
            return json_files

        elif choice == options[1]:  # Select specific files from raw_data
            return self.select_specific_files(self.default_data_dir)

        elif choice == options[2]:  # Different directory
            custom_dir = self.get_custom_path("Enter directory path", must_exist=True)
            if not custom_dir.is_dir():
                print("❌ Path is not a directory!")
                return self.select_input_files()

            json_files = list(custom_dir.glob("*.json"))
            if not json_files:
                print(f"❌ No JSON files found in '{custom_dir}'!")
                return self.select_input_files()

            print(f"✅ Selected {len(json_files)} files from '{custom_dir}'")
            return json_files

        elif choice == options[3]:  # Individual files
            return self.select_individual_files()

        # This should never be reached, but added for type safety
        return []

    def select_specific_files(self, directory: Path) -> List[Path]:
        """
        Let user select specific files from a directory.

        Args:
            directory: Directory to select files from

        Returns:
            List of selected file paths
        """
        json_files = list(directory.glob("*.json"))
        if not json_files:
            print(f"❌ No JSON files found in '{directory}'!")
            return []

        print(f"\n📋 FILES IN '{directory}':")
        print("-" * 40)

        for i, file in enumerate(json_files, 1):
            file_size = file.stat().st_size / 1024  # Size in KB
            print(f"  {i:2d}. {file.name} ({file_size:.1f} KB)")

        print(f"\nEnter file numbers to process (e.g., '1,3,5' or '1-10' or 'all'):")
        selection = input("Selection: ").strip().lower()

        if selection == 'all':
            return json_files

        selected_files = []
        try:
            # Parse selection (handle ranges and individual numbers)
            for part in selection.split(','):
                part = part.strip()
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    selected_files.extend(json_files[start-1:end])
                else:
                    selected_files.append(json_files[int(part)-1])

            print(f"✅ Selected {len(selected_files)} files")
            return selected_files

        except (ValueError, IndexError):
            print("❌ Invalid selection format!")
            return self.select_specific_files(directory)

    def select_individual_files(self) -> List[Path]:
        """
        Let user select individual files from anywhere.

        Returns:
            List of selected file paths
        """
        files = []
        print("\n📄 SELECT INDIVIDUAL FILES")
        print("-" * 30)
        print("Enter file paths one by one (press Enter with empty input to finish):")

        while True:
            file_path = input(f"File {len(files)+1} (or Enter to finish): ").strip()
            if not file_path:
                break

            path = Path(file_path)
            if not path.exists():
                print(f"❌ File does not exist: {path}")
                continue

            if not path.suffix.lower() == '.json':
                print(f"❌ File is not a JSON file: {path}")
                continue

            files.append(path)
            print(f"✅ Added: {path.name}")

        if not files:
            print("❌ No files selected!")
            return self.select_input_files()

        return files

    def configure_output(self) -> Tuple[Any, str]:
        """
        Let user configure output settings.

        Returns:
            Tuple of (output_directory, filename)
        """
        print("\n💾 CONFIGURE OUTPUT")
        print("-" * 25)

        # Output directory selection
        dir_options = [
            f"Current directory ({self.default_output_dir.absolute()})",
            "Same directory as input files",
            "Choose custom directory"
        ]

        dir_choice = self.get_user_choice("Where should the output file be saved?", dir_options)

        if dir_choice == dir_options[0]:  # Current directory
            output_dir = self.default_output_dir
        elif dir_choice == dir_options[1]:  # Same as input
            output_dir = "INPUT_DIR"  # Special marker
        else:  # Custom directory
            output_dir = self.get_custom_path("Enter output directory", must_exist=True)
            if not output_dir.is_dir():
                print("❌ Path is not a directory!")
                return self.configure_output()

        # Filename configuration
        print("\n📝 OUTPUT FILENAME")
        print("-" * 20)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"coloring_features_{timestamp}.csv"

        filename_options = [
            f"Use default filename: {default_filename}",
            "Enter custom filename"
        ]

        filename_choice = self.get_user_choice("Choose filename option:", filename_options)

        if filename_choice == filename_options[0]:  # Default
            filename = default_filename
        else:  # Custom
            while True:
                filename = input("Enter filename (with .csv extension): ").strip()
                if not filename:
                    print("❌ Please enter a filename")
                    continue
                if not filename.endswith('.csv'):
                    filename += '.csv'
                break

        return output_dir, filename

    def preview_results(self, df: pd.DataFrame) -> bool:
        """
        Show preview of results and ask if user wants to save.

        Args:
            df: DataFrame with extracted features

        Returns:
            True if user wants to save, False otherwise
        """
        print("\n📊 RESULTS PREVIEW")
        print("-" * 25)
        print(f"✅ Successfully processed {len(df)} sessions")
        print(f"📈 Extracted {len(df.columns)} features per session")

        print(f"\n🔍 FEATURE COLUMNS ({len(df.columns)} total):")
        print("-" * 40)
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")

        print(f"\n📋 SAMPLE SESSION DATA (First Session - Dictionary Format):")
        print("-" * 65)

        # Display first session as a vertical dictionary format
        if len(df) > 0:
            first_session = df.iloc[0]  # Get first row
            print("Session 1 Features:")
            print("{")

            # Display each feature as a key-value pair
            for i, (feature, value) in enumerate(first_session.items()):
                # Format the value based on its type
                if isinstance(value, (int, float)):
                    if isinstance(value, float):
                        # Round floats to 4 decimal places for readability
                        formatted_value = f"{value:.4f}" if abs(value) < 1000 else f"{value:.2f}"
                    else:
                        formatted_value = str(value)
                else:
                    # String values - add quotes
                    formatted_value = f"'{value}'"

                # Add comma except for last item
                comma = "," if i < len(first_session) - 1 else ""
                print(f"    '{feature}': {formatted_value}{comma}")

            print("}")

        # Also show a compact horizontal summary for multiple sessions if available
        if len(df) > 1:
            print(f"\n📊 SESSIONS OVERVIEW ({len(df)} total sessions):")
            print("-" * 50)
            # Show key metrics for all sessions in a compact format
            key_cols = ['filename', 'total_time_spent', 'total_sequences', 'final_completion_percentage']
            available_key_cols = [col for col in key_cols if col in df.columns]

            if available_key_cols:
                # Show first 3 sessions with key metrics
                display_df = df[available_key_cols].head(3)
                print(display_df.to_string(index=False))
                if len(df) > 3:
                    print(f"... and {len(df) - 3} more sessions")
            else:
                print("Multiple sessions processed - see summary statistics below")

        print(f"\n📊 SUMMARY STATISTICS:")
        print("-" * 30)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            summary = df[numeric_cols].describe()
            print(summary.round(2))

        # Ask if user wants to save
        save_options = ["Yes, save the results", "No, don't save"]
        save_choice = self.get_user_choice("\nDo you want to save these results?", save_options)

        return save_choice == save_options[0]

    def process_files(self, files: List[Path]) -> pd.DataFrame:
        """
        Process selected files and extract features.

        Args:
            files: List of JSON files to process

        Returns:
            DataFrame with extracted session features
        """
        print(f"\n⚙️  PROCESSING {len(files)} FILES")
        print("-" * 40)

        all_session_summaries = []
        processed_count = 0
        skipped_count = 0
        error_count = 0

        for i, json_file in enumerate(files, 1):
            print(f"📄 Processing {i}/{len(files)}: {json_file.name}", end=" ... ")

            try:
                # Load and parse the JSON file
                session_data = self.extractor.load_json_file(str(json_file))

                # Check if session is valid
                if self.extractor.is_valid_session(session_data):
                    # Process this single file using the new method
                    touch_data = session_data.get('touchData', {})

                    # Calculate features for every sequence in the session
                    sequences_features = []
                    all_events = []
                    for seq_id in sorted(touch_data.keys(), key=int):
                        cleaned_events = self.extractor.clean_sequence_events(touch_data[seq_id])
                        if cleaned_events:
                            features = self.extractor.calculate_sequence_features(cleaned_events, int(seq_id))
                            if features:
                                sequences_features.append(features)
                                all_events.extend(cleaned_events)

                    if sequences_features:
                        # Aggregate for session summary
                        all_events.sort(key=lambda x: x['time'])
                        session_summary = self.extractor.calculate_session_features(sequences_features, all_events, json_file.name)
                        all_session_summaries.append(session_summary)
                        processed_count += 1
                        print("✅ Success")
                    else:
                        skipped_count += 1
                        print("⚠️  Skipped (no valid sequences)")
                else:
                    skipped_count += 1
                    print("⚠️  Skipped (insufficient data)")

            except Exception as e:
                error_count += 1
                print(f"❌ Error: {str(e)[:50]}...")

        # Print summary
        print(f"\n📊 PROCESSING SUMMARY")
        print("-" * 30)
        print(f"✅ Successfully processed: {processed_count}")
        print(f"⚠️  Skipped (invalid): {skipped_count}")
        print(f"❌ Errors: {error_count}")
        print(f"📁 Total files: {len(files)}")

        if not all_session_summaries:
            print("\n❌ No valid sessions found! Check your data files.")
            return pd.DataFrame()

        # Create DataFrame
        df = pd.DataFrame(all_session_summaries)

        # Clean up problematic values
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(0)

        return df

    def run(self):
        """
        Main CLI workflow - run the complete interactive process.
        """
        try:
            # Display welcome banner
            self.display_banner()

            # Step 1: Select input files
            files = self.select_input_files()
            if not files:
                print("❌ No files selected. Exiting.")
                return

            # Step 2: Configure output
            output_dir, filename = self.configure_output()

            # Step 3: Process files
            df = self.process_files(files)
            if df.empty:
                print("❌ No features extracted. Exiting.")
                return

            # Step 4: Preview results
            if not self.preview_results(df):
                print("🚫 Results not saved (user choice).")
                return

            # Step 5: Save results
            if output_dir == "INPUT_DIR":
                # Use directory of first input file
                output_dir = files[0].parent

            output_path = Path(output_dir) / filename

            try:
                df.to_csv(output_path, index=False)
                print(f"\n💾 Results saved successfully!")
                print(f"📁 File: {output_path.absolute()}")
                print(f"📊 Sessions: {len(df)}")
                print(f"📈 Features: {len(df.columns)}")

            except Exception as e:
                print(f"\n❌ Error saving file: {e}")
                print("💡 Try choosing a different output directory.")

        except KeyboardInterrupt:
            print("\n\n🛑 Process interrupted by user. Goodbye!")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            print("💡 Please check your input files and try again.")


# === UPDATED MAIN FUNCTION ===

def main():
    """
    Main function that runs the interactive CLI for feature extraction.

    This function provides two modes:
    1. Interactive CLI mode (default) - User-friendly interface with menus
    2. Simple mode (if raw_data directory exists) - Quick processing option
    """
    print("🎨 Coloring Feature Extractor")
    print("=" * 40)

    # Check if raw_data directory exists for quick mode option
    raw_data_dir = Path("raw_data")

    if raw_data_dir.exists() and list(raw_data_dir.glob("*.json")):
        json_count = len(list(raw_data_dir.glob("*.json")))
        print(f"📁 Found {json_count} JSON files in 'raw_data' directory")
        print()

        # Offer quick mode or interactive mode
        mode_options = [
            f"🚀 Quick mode: Process all {json_count} files in raw_data",
            "🎛️  Interactive mode: Full control over file selection and output"
        ]

        try:
            print("Choose processing mode:")
            for i, option in enumerate(mode_options, 1):
                print(f"  {i}. {option}")

            choice = input("\nEnter your choice (1 or 2): ").strip()

            if choice == "1":
                # Quick mode - process all files in raw_data
                print(f"\n🚀 QUICK MODE: Processing all files in raw_data")
                print("-" * 50)

                extractor = ColoringFeatureExtractor()
                session_df, interaction_df = extractor.process_directory(str(raw_data_dir))

                if not session_df.empty:
                    # Generate timestamped filename
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    session_output_file = f"session_features_{timestamp}.csv"
                    interaction_output_file = f"interaction_features_{timestamp}.csv"

                    session_df.to_csv(session_output_file, index=False)

                    print(f"\n✅ SUCCESS!")
                    print(f"📊 Processed {len(session_df)} sessions")
                    print(f"📈 Extracted {len(session_df.columns)} session-level features")
                    print(f"💾 Session features saved to: {session_output_file}")

                    if not interaction_df.empty:
                        interaction_df.to_csv(interaction_output_file, index=False)
                        print(f"� Extracted {len(interaction_df)} individual interactions")
                        print(f"📈 With {len(interaction_df.columns)} interaction-level features")
                        print(f"💾 Interaction features saved to: {interaction_output_file}")

                    # Show quick preview
                    print(f"\n📋 Sample session features: {list(session_df.columns[:5])}...")
                else:
                    print("❌ No features extracted - check your data files")
                return

            elif choice == "2":
                # Interactive mode
                pass  # Continue to CLI below
            else:
                print("❌ Invalid choice, starting interactive mode...")

        except KeyboardInterrupt:
            print("\n🛑 Goodbye!")
            return

    # Run interactive CLI
    cli = ColoringFeatureExtractorCLI()
    cli.run()


def simple_batch_process(directory_path: str = "raw_data"):
    """
    Simple function for batch processing (for programmatic use).

    Args:
        directory_path: Path to directory containing JSON files

    Returns:
        Tuple of (session_df, interaction_df) with extracted features
    """
    extractor = ColoringFeatureExtractor()
    return extractor.process_directory(directory_path)


# This runs the main function when the script is executed directly
# (but not when imported as a module)
if __name__ == "__main__":
    main()