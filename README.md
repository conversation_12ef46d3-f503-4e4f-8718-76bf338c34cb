## Overview

A comprehensive behavioral analysis engine that transforms raw touch data from digital coloring sessions into structured feature sets for research, clinical assessment, and machine learning applications.

## Overview


## Quick Start

1. **Place your JSON files** in the `raw_data` directory
2. **Run the script**: `python3 coloring_feature_extractor.py`
3. **Choose your mode**:
   - **Quick Mode**: Automatically processes all files in `raw_data`
   - **Interactive Mode**: Full control over file selection and output options

## Features

### 🚀 Quick Mode
- Automatically processes all JSON files in the `raw_data` directory
- Uses timestamped output filenames
- Perfect for batch processing

### 🎛️ Interactive Mode
- **File Selection Options**:
  - Process all files in `raw_data`
  - Select specific files from `raw_data`
  - Choose a different directory
  - Select individual files from anywhere

- **Output Configuration**:
  - Choose output directory (current, same as input, or custom)
  - Set custom filename or use auto-generated timestamped names


- **Results Preview**:
  - View extracted features before saving
  - **Vertical dictionary format** for easy feature review
  - Complete feature list with all 18 behavioral metrics
  - Summary statistics across all sessions
  - Sessions overview table for multiple files
  - Option to save or discard results

## Technical Architecture

### Data Processing Pipeline
```
Raw JSON → Touch Sequences → Data Cleaning → Kinematic Analysis → Feature Extraction → Structured Output
```

### Stabilization Techniques
1. **Coordinate Smoothing**: 3-point rolling window to reduce sensor noise
2. **Time Threshold Filtering**: 1ms minimum intervals to prevent numerical instabilities
3. **Quantile-Based Clipping**: 1st/99th percentile bounds for outlier removal
4. **Spatial Outlier Filtering**: Removes coordinates beyond valid screen bounds

### Interaction Classification
- **Tap**: Brief stationary touches (< 0.25s, < 15px movement)
- **Drag**: Movement-based interactions (≥ 15px movement)
- **Hold**: Extended stationary touches (≥ 0.25s, < 15px movement)

## Extracted Features

The system extracts comprehensive behavioral features at two complementary levels:

### Sequence-Level Features (25 features per touch sequence)

#### Identification Features
- `sequence_id` - Unique sequence identifier
- `sequence_type` - Classification: 'Tap'/'Drag'/'Hold'
- `start_color` - Color selected at sequence start

#### Temporal Features
- `start_time` - Sequence start timestamp
- `end_time` - Sequence end timestamp
- `duration` - Total time span (seconds)

#### Spatial Features
- `path_length` - Total distance traveled (pixels)
- `path_straightness` - Movement efficiency ratio [0,1]
- `drag_area` - Bounding box area (pixels²)
- `direction_changes` - Count of significant direction changes

#### Kinematic Features (Velocity)
- `velocity_mean` - Average movement speed (pixels/sec)
- `velocity_median` - Median movement speed (pixels/sec)
- `avg_speed` - Overall average speed (pixels/sec)
- `speed_variability` - Speed standard deviation

#### Kinematic Features (Acceleration)
- `acc_mean` - Average acceleration (stabilized)
- `acc_median` - Median acceleration (stabilized)

#### Kinematic Features (Jerk)
- `jerk_mean` - Average jerk (stabilized)
- `jerk_median` - Median jerk (stabilized)

#### Accelerometer Features
- `acc_magnitude_mean` - Average 3D acceleration magnitude
- `acc_magnitude_std` - 3D acceleration variability

#### Spatial Zone Features
- `primary_zone` - First zone encountered
- `proportion_in_primary_zone` - Time in primary zone
- `zone_crossings` - Number of zone transitions
- `zone_consistency` - Binary: stayed in same zone
- `error_rate` - Proportion outside valid zones

### Session-Level Features (28 features per session)

#### Identification
- `filename` - Session file identifier

#### Temporal Patterns
- `total_time_spent` - Total session duration (seconds)
- `total_sequences` - Total number of interactions
- `avg_sequence_duration` - Average interaction length (seconds)
- `avg_time_between_sequences` - Average pause between interactions
- `longest_pause` - Longest single pause (seconds)

#### Kinematic Aggregations
- `avg_velocity_mean` - Session-wide average velocity
- `avg_acc_mean` - Session-wide average acceleration
- `avg_jerk_mean` - Session-wide average jerk
- `total_area` - Total area covered by all sequences
- `shortest_drag_id` - Sequence ID of shortest drag
- `longest_drag_id` - Sequence ID of longest drag

#### Behavioral Patterns
- `sequence_type_transition_rate` - Frequency of interaction type changes
- `color_switch_frequency` - Number of color changes
- `repetitive_actions` - Number of repetitive behaviors

#### Preferences and Performance
- `most_frequent_zone` - Preferred coloring area
- `most_frequent_color` - Preferred color
- `final_completion_percentage` - Task completion percentage
- `completion_efficiency` - Progress per unit time

#### Interaction Type Counts
- `drag_count` - Number of drawing/painting movements
- `tap_count` - Number of quick touches
- `hold_count` - Number of sustained touches

#### Motor Control Quality
- `average_path_length_drag` - Average distance of drawing movements
- `average_straightness_drag` - Drawing movement efficiency
- `erratic_drags_count` - Number of erratic/shaky movements

#### Precision and Attention
- `average_tap_duration` - Average quick touch duration
- `proportion_of_taps_in_valid_zones` - Quick touch accuracy
- `average_hold_duration` - Average sustained touch duration

## Data Quality and Validation

### Robust Data Processing
- **Automatic Data Cleaning**: Removes coordinate outliers (>2000px) and duplicate timestamps
- **Session Validation**: Filters sessions with insufficient data (< 5 sequences or < 10 seconds)
- **Numerical Stability**: Time thresholds prevent division-by-zero errors
- **Outlier Management**: Quantile-based clipping handles extreme kinematic values

### Quality Metrics
- **Data Integrity**: Zero NaN or infinity values in output
- **Feature Completeness**: All 25 sequence + 28 session features reliably extracted
- **Processing Statistics**: Real-time feedback on data quality and processing success

## Preview Format

The CLI displays extracted features in an easy-to-read vertical dictionary format:

```
📋 SAMPLE SESSION DATA (First Session - Dictionary Format):
Session 1 Features:
{
    'filename': 'session.json',
    'total_time_spent': 165.7321,
    'total_sequences': 33,
    'avg_sequence_duration': 1.8181,
    'avg_time_between_sequences': 6.3552,
    'longest_pause': 83.5936,
    'avg_velocity_mean': 1955.234,
    'avg_acc_mean': 145.678,
    'avg_jerk_mean': -234.567,
    'sequence_type_transition_rate': 0.1212,
    'color_switch_frequency': 0,
    'repetitive_actions': 13,
    'most_frequent_zone': 'Outside',
    'most_frequent_color': 'Green',
    'final_completion_percentage': 95.7806,
    'completion_efficiency': 0.5779,
    'drag_count': 29,
    'tap_count': 3,
    'hold_count': 1,
    'average_path_length_drag': 5808.78,
    'average_straightness_drag': 0.2665,
    'erratic_drags_count': 13,
    'average_tap_duration': 0.0557,
    'proportion_of_taps_in_valid_zones': 0.6667,
    'average_hold_duration': 0.9699
}
```

For multiple sessions, you'll also see:
- **Sessions Overview**: Key metrics for all processed sessions
- **Summary Statistics**: Mean, std, min, max across all sessions
- **Data Quality Report**: Processing success rates and validation results

## Usage Examples

### Quick Processing
```bash
python3 coloring_feature_extractor.py
# Choose option 1 for quick mode
```

### Interactive Processing
```bash
python3 coloring_feature_extractor.py
# Choose option 2 for interactive mode
# Follow the prompts to customize your processing
```

### Programmatic Use
```python
from coloring_feature_extractor import ColoringFeatureExtractor, simple_batch_process

# Method 1: Direct class usage for custom processing
extractor = ColoringFeatureExtractor()
session_df, interaction_df = extractor.process_directory("raw_data")

# Method 2: Simple batch processing function
session_df, interaction_df = simple_batch_process("raw_data")

print(f"Processed {len(session_df)} sessions")
print(f"Extracted {len(interaction_df)} individual interactions")
print(f"Session features: {session_df.shape[1]} columns")
print(f"Interaction features: {interaction_df.shape[1]} columns")

# Access specific features
print(f"Average session duration: {session_df['total_time_spent'].mean():.2f} seconds")
print(f"Most common interaction type: {interaction_df['sequence_type'].mode()[0]}")
```

### Advanced Usage
```python
# Process individual sequences
extractor = ColoringFeatureExtractor()

# Load and validate session data
session_data = extractor.load_json_file("path/to/session.json")
if extractor.is_valid_session(session_data):
    # Extract features from specific sequence
    events = session_data['touchData']['1']  # First sequence
    features = extractor.calculate_sequence_features(events, sequence_id=1)
    print(f"Sequence type: {features['sequence_type']}")
    print(f"Duration: {features['duration']:.3f} seconds")
    print(f"Path length: {features['path_length']:.1f} pixels")
```

## File Structure
```
project/
├── coloring_feature_extractor.py  # Main script
├── raw_data/                      # Input JSON files
│   ├── session1.json
│   ├── session2.json
│   └── ...
├── session_features_YYYYMMDD_HHMMSS.csv    # Session-level output
├── interaction_features_YYYYMMDD_HHMMSS.csv # Interaction-level output
└── README.md                      # This file
```

## Requirements
- Python 3.6+
- pandas
- numpy

## Installation
```bash
# Clone or download the repository
git clone <repository-url>
cd coloring-feature-extractor

# Install dependencies
pip install pandas numpy

# Verify installation
python3 coloring_feature_extractor.py --help
```

## Performance and Scalability

### Processing Speed
- **Typical Performance**: ~100-500 sequences per second depending on sequence complexity
- **Memory Efficiency**: Processes files individually to handle large datasets
- **Batch Processing**: Optimized for processing multiple sessions simultaneously

### Scalability Features
- **Robust Error Handling**: Continues processing even if individual files fail
- **Memory Management**: Efficient DataFrame operations for large datasets
- **Progress Tracking**: Real-time feedback for long-running batch jobs

## Error Handling and Validation

### Automatic Data Validation
- **File Format Validation**: Checks JSON structure and required fields
- **Session Quality Filtering**: Removes sessions with insufficient data
- **Coordinate Validation**: Filters out sensor glitches and invalid touch data
- **Temporal Validation**: Handles duplicate timestamps and time sequence issues

### Error Recovery
- **Graceful Degradation**: Skips problematic files while continuing batch processing
- **Detailed Error Reporting**: Clear messages for debugging data issues
- **Keyboard Interrupt Support**: Clean shutdown with Ctrl+C

### Data Quality Assurance
- **Zero NaN Values**: Robust calculations prevent missing data in output
- **Numerical Stability**: Time thresholds and clipping prevent mathematical errors
- **Feature Completeness**: All expected features reliably extracted for valid sessions


## Tips and Best Practices
- **Data Organization**: Place all JSON files in the `raw_data` directory for easiest processing
- **Processing Modes**: Use Quick Mode for routine batch processing, Interactive Mode for custom workflows
- **Output Management**: Files are timestamped to prevent accidental overwrites
- **Quality Control**: Review processing statistics to ensure data quality
- **Feature Selection**: Use the comprehensive feature set for initial analysis, then focus on relevant subsets

## Technical Implementation Details

### Stabilization Pipeline
The feature extraction engine implements a comprehensive stabilization pipeline to ensure reliable results:

1. **Data Cleaning**: Removes coordinate outliers (>2000px) and duplicate timestamps
2. **Coordinate Smoothing**: 3-point rolling window reduces sensor noise
3. **Time Threshold Filtering**: 1ms minimum intervals prevent numerical instabilities
4. **Quantile-Based Clipping**: 1st/99th percentile bounds remove extreme kinematic outliers

### Kinematic Analysis
Advanced kinematic feature calculation with robust error handling:
- **Velocity**: Calculated using smoothed coordinates with time threshold protection
- **Acceleration**: First derivative of velocity with conditional calculation
- **Jerk**: Second derivative of velocity for movement quality assessment
- **Statistical Aggregation**: Mean and median values using stabilized data

### Code Architecture
- **Object-Oriented Design**: Clean separation of concerns with modular methods
- **Defensive Programming**: Extensive error checking and graceful degradation
- **Vectorized Operations**: Efficient pandas/numpy operations for performance
- **Comprehensive Documentation**: Technical implementation details for developers

## Version Information

### Current Version: 2.0 (Robust Implementation)
**Key Improvements:**
- ✅ Enhanced kinematic stabilization pipeline
- ✅ Expanded feature set (25 sequence + 28 session features)
- ✅ Improved numerical stability and error handling
- ✅ Comprehensive technical documentation
- ✅ Optimized performance and memory efficiency
- ✅ Clinical-grade data quality assurance

### Previous Versions:
- **v1.0**: Basic feature extraction with 18 sequence features
- **v1.5**: Added session-level aggregation and CLI interface

---

**For technical support or feature requests, please refer to the comprehensive inline documentation in the `ColoringFeatureExtractor` class.**
