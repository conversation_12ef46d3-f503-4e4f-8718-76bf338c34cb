# Comprehensive Feature Documentation
## Coloring Behavioral Analysis Engine

This document provides detailed technical specifications for all 53 features extracted by the Coloring Feature Extractor system (25 sequence-level + 28 session-level features).

---

## SEQUENCE-LEVEL FEATURES (25 features per touch sequence)

### IDENTIFICATION FEATURES

#### sequence_id
- **Description**: Unique identifier for each touch sequence within a session
- **Data Type**: Integer
- **Calculation**: Extracted directly from the JSON touchData keys (e.g., "0", "1", "2")
- **Range**: 0 to N-1 where N is the total number of sequences
- **Purpose**: Links individual sequences back to their original data structure

#### sequence_type
- **Description**: Classification of the touch interaction based on movement and duration patterns
- **Data Type**: String (categorical)
- **Calculation**: Rule-based classification algorithm:
  ```python
  if path_length >= 15:  # pixels
      return "Drag"      # Movement-based interaction
  elif duration < 0.25:  # seconds
      return "Tap"       # Brief touch
  else:
      return "Hold"      # Sustained stationary touch
  ```
- **Values**: "Tap", "Drag", "Hold"
- **Purpose**: Categorizes interaction intent and motor behavior patterns

#### start_color
- **Description**: The color selected at the beginning of the touch sequence
- **Data Type**: String
- **Calculation**: `events[0]['color']` - extracted from the first event in the sequence
- **Values**: Color names (e.g., "RedDefault", "Blue", "Green")
- **Purpose**: Tracks color preferences and switching patterns

### TEMPORAL FEATURES

#### start_time
- **Description**: Timestamp when the touch sequence began
- **Data Type**: Float (seconds)
- **Calculation**: `events[0]['time']` - timestamp of the first touch event
- **Range**: Positive floating-point values representing seconds since session start
- **Purpose**: Enables temporal analysis and sequence ordering

#### end_time
- **Description**: Timestamp when the touch sequence ended
- **Data Type**: Float (seconds)
- **Calculation**: `events[-1]['time']` - timestamp of the last touch event
- **Range**: Positive floating-point values, always ≥ start_time
- **Purpose**: Defines sequence boundaries for duration calculation

#### duration
- **Description**: Total time span of the touch sequence
- **Data Type**: Float (seconds)
- **Calculation**: `events[-1]['time'] - events[0]['time']`
- **Range**: ≥ 0 seconds (typically 0.01 to 10+ seconds)
- **Purpose**: Measures interaction engagement and motor planning time

### SPATIAL FEATURES

#### path_length
- **Description**: Total distance traveled by the finger during the sequence
- **Data Type**: Float (pixels)
- **Calculation**: Sum of Euclidean distances between consecutive smoothed coordinates:
  ```python
  path_length = sum(sqrt((x[i+1]-x[i])² + (y[i+1]-y[i])²) for i in range(1, len(events)))
  ```
- **Pre-processing**: Coordinates smoothed using 3-point rolling window
- **Range**: ≥ 0 pixels (0 for stationary touches, 100s-1000s for long drags)
- **Purpose**: Quantifies movement extent and motor activity level

#### path_straightness
- **Description**: Movement efficiency ratio measuring how direct the path was
- **Data Type**: Float (ratio)
- **Calculation**: `direct_distance / path_length` where direct_distance is Euclidean distance between start and end points
- **Range**: 0.0 to 1.0 (1.0 = perfectly straight line, lower values = more curved/erratic)
- **Purpose**: Assesses motor control quality and movement planning efficiency

#### drag_area
- **Description**: Area of the bounding rectangle encompassing all touch points
- **Data Type**: Float (pixels²)
- **Calculation**: `(x_max - x_min) × (y_max - y_min)` using smoothed coordinates
- **Range**: ≥ 0 pixels² (0 for single points, large values for expansive movements)
- **Purpose**: Measures spatial extent and coverage of the interaction

#### direction_changes
- **Description**: Count of significant directional changes during movement
- **Data Type**: Integer
- **Calculation**: Counts angular changes > π/6 radians (30°) between consecutive movement vectors
- **Algorithm**:
  ```python
  for consecutive coordinate triplets:
      calculate angle between vectors
      if angle > π/6: increment counter
  ```
- **Range**: ≥ 0 (0 = straight movement, higher values = more erratic)
- **Purpose**: Quantifies movement smoothness and motor control stability

### KINEMATIC FEATURES (VELOCITY)

#### velocity_mean
- **Description**: Average movement speed throughout the sequence
- **Data Type**: Float (pixels/second)
- **Calculation**: Mean of instantaneous velocities with time threshold protection:
  ```python
  velocity[i] = distance[i] / time_delta[i] if time_delta[i] >= 0.001 else 0
  velocity_mean = mean(velocity)
  ```
- **Stabilization**: 1ms minimum time threshold prevents division by near-zero values
- **Range**: ≥ 0 pixels/second (typically 10-5000 for normal interactions)
- **Purpose**: Measures overall movement speed and motor tempo

#### velocity_median
- **Description**: Median movement speed, robust to outliers
- **Data Type**: Float (pixels/second)
- **Calculation**: `median(instantaneous_velocities)` using same velocity calculation as above
- **Range**: ≥ 0 pixels/second
- **Purpose**: Provides outlier-resistant measure of typical movement speed

#### avg_speed
- **Description**: Overall average speed calculated from total distance and time
- **Data Type**: Float (pixels/second)
- **Calculation**: `path_length / duration` if duration > 0, else 0
- **Range**: ≥ 0 pixels/second
- **Purpose**: Global speed measure independent of sampling rate variations

#### speed_variability
- **Description**: Standard deviation of instantaneous speeds
- **Data Type**: Float (pixels/second)
- **Calculation**: `std(instantaneous_velocities)` excluding zero time deltas
- **Range**: ≥ 0 pixels/second (0 = constant speed, higher = more variable)
- **Purpose**: Measures movement consistency and motor control smoothness

### KINEMATIC FEATURES (ACCELERATION)

#### acc_mean
- **Description**: Average acceleration magnitude using stabilized calculation
- **Data Type**: Float (pixels/second²)
- **Calculation**: 
  ```python
  acceleration[i] = velocity_change[i] / time_delta[i] if time_delta[i] >= 0.001 else 0
  acc_clipped = clip(acceleration, 1st_percentile, 99th_percentile)
  acc_mean = mean(acc_clipped)
  ```
- **Stabilization**: Time threshold + quantile clipping for numerical stability
- **Range**: Real numbers (can be negative, typically -1000 to +1000)
- **Purpose**: Measures acceleration patterns and movement control quality

#### acc_median
- **Description**: Median acceleration magnitude, robust to outliers
- **Data Type**: Float (pixels/second²)
- **Calculation**: `median(acc_clipped)` using same stabilized acceleration values
- **Range**: Real numbers
- **Purpose**: Outlier-resistant measure of typical acceleration behavior

### KINEMATIC FEATURES (JERK)

#### jerk_mean
- **Description**: Average jerk (rate of acceleration change) using stabilized calculation
- **Data Type**: Float (pixels/second³)
- **Calculation**:
  ```python
  jerk[i] = acceleration_change[i] / time_delta[i] if time_delta[i] >= 0.001 else 0
  jerk_clipped = clip(jerk, 1st_percentile, 99th_percentile)
  jerk_mean = mean(jerk_clipped)
  ```
- **Stabilization**: Time threshold + quantile clipping for extreme outlier removal
- **Range**: Real numbers (can be negative, typically -10000 to +10000)
- **Purpose**: Measures movement smoothness and neuromotor control quality

#### jerk_median
- **Description**: Median jerk magnitude, robust to outliers
- **Data Type**: Float (pixels/second³)
- **Calculation**: `median(jerk_clipped)` using same stabilized jerk values
- **Range**: Real numbers
- **Purpose**: Outlier-resistant measure of typical movement smoothness

### ACCELEROMETER FEATURES

#### acc_magnitude_mean
- **Description**: Average magnitude of 3D accelerometer readings
- **Data Type**: Float (m/s² or device units)
- **Calculation**: `mean(sqrt(accX² + accY² + accZ²))` for all events with accelerometer data
- **Range**: ≥ 0 (typically 0.5-2.0 for normal device handling)
- **Purpose**: Measures device movement and handling stability

#### acc_magnitude_std
- **Description**: Standard deviation of 3D accelerometer magnitudes
- **Data Type**: Float (m/s² or device units)
- **Calculation**: `std(sqrt(accX² + accY² + accZ²))` for all events with accelerometer data
- **Range**: ≥ 0 (higher values indicate more variable device movement)
- **Purpose**: Quantifies device handling consistency and tremor-like movements

### SPATIAL ZONE FEATURES

#### primary_zone
- **Description**: The first spatial zone encountered in the sequence
- **Data Type**: String (categorical)
- **Calculation**: `events[0]['zone']` - zone value from the first touch event
- **Values**: Zone names (e.g., "Zone1", "Zone2", "Outside", "Super outside")
- **Purpose**: Identifies initial targeting and spatial preferences

#### proportion_in_primary_zone
- **Description**: Fraction of sequence time spent in the primary zone
- **Data Type**: Float (proportion)
- **Calculation**: `time_in_primary_zone / total_duration`
- **Range**: 0.0 to 1.0 (1.0 = stayed entirely in primary zone)
- **Purpose**: Measures spatial consistency and targeting accuracy

#### zone_crossings
- **Description**: Number of transitions between different spatial zones
- **Data Type**: Integer
- **Calculation**: Count of times `events[i]['zone'] != events[i-1]['zone']`
- **Range**: ≥ 0 (0 = stayed in one zone, higher = more zone transitions)
- **Purpose**: Quantifies spatial exploration and boundary crossing behavior

#### zone_consistency
- **Description**: Binary indicator of whether the sequence stayed in one zone
- **Data Type**: Integer (0 or 1)
- **Calculation**: `1 if zone_crossings == 0 else 0`
- **Values**: 0 (crossed zones) or 1 (stayed in one zone)
- **Purpose**: Simple measure of spatial targeting consistency

#### error_rate
- **Description**: Proportion of sequence time spent outside valid coloring zones
- **Data Type**: Float (proportion)
- **Calculation**: `time_in_error_zones / total_duration` where error zones are "Outside" and "Super outside"
- **Range**: 0.0 to 1.0 (0.0 = perfect accuracy, 1.0 = entirely outside valid areas)
- **Purpose**: Measures targeting accuracy and task compliance

---

## SESSION-LEVEL FEATURES (28 features per session)

### IDENTIFICATION

#### filename
- **Description**: Identifier for the session data file
- **Data Type**: String
- **Calculation**: Extracted from the JSON filename
- **Purpose**: Links session features back to original data source

### TEMPORAL PATTERNS

#### total_time_spent
- **Description**: Total elapsed time from first to last touch event in the session
- **Data Type**: Float (seconds)
- **Calculation**: `max(all_event_times) - min(all_event_times)` across all sequences
- **Individual Basis**: Each sequence contributes its start_time and end_time to the global timeline
- **Range**: > 0 seconds (typically 30-1800 seconds for complete sessions)
- **Purpose**: Measures overall session engagement and task completion time

#### total_sequences
- **Description**: Total count of distinct touch interactions in the session
- **Data Type**: Integer
- **Calculation**: Count of top-level keys in touchData object (e.g., "0", "1", "2")
- **Range**: ≥ 5 (minimum for valid sessions, typically 10-200)
- **Purpose**: Quantifies interaction frequency and engagement level

#### avg_sequence_duration
- **Description**: Arithmetic mean of all individual sequence durations
- **Data Type**: Float (seconds)
- **Calculation**: `mean([seq.duration for seq in all_sequences])`
- **Individual Basis**: Each sequence duration = `events[-1]['time'] - events[0]['time']`
- **Range**: > 0 seconds (typically 0.5-5.0 seconds)
- **Purpose**: Measures typical interaction length and engagement depth

#### avg_time_between_sequences
- **Description**: Average pause time between consecutive sequences
- **Data Type**: Float (seconds)
- **Calculation**: `mean([seq[i].start_time - seq[i-1].end_time for consecutive sequences])`
- **Individual Basis**: Gap = next_sequence.start_time - current_sequence.end_time
- **Range**: ≥ 0 seconds (typically 1-30 seconds)
- **Purpose**: Measures planning time, hesitation, and cognitive processing

#### longest_pause
- **Description**: Single longest gap between any two consecutive sequences
- **Data Type**: Float (seconds)
- **Calculation**: `max([seq[i].start_time - seq[i-1].end_time for consecutive sequences])`
- **Individual Basis**: Same gap calculation as above, taking maximum
- **Range**: ≥ 0 seconds (can be minutes for sessions with long breaks)
- **Purpose**: Identifies major interruptions or difficulty points

### KINEMATIC AGGREGATIONS

#### avg_velocity_mean
- **Description**: Session-wide average of individual sequence velocity means
- **Data Type**: Float (pixels/second)
- **Calculation**: `mean([seq.velocity_mean for seq in all_sequences])`
- **Individual Basis**: Each sequence velocity_mean = `mean(instantaneous_velocities)` with time threshold protection
- **Range**: ≥ 0 pixels/second (typically 100-3000)
- **Purpose**: Overall movement speed characterization across session

#### avg_acc_mean
- **Description**: Session-wide average of individual sequence acceleration means
- **Data Type**: Float (pixels/second²)
- **Calculation**: `mean([seq.acc_mean for seq in all_sequences])`
- **Individual Basis**: Each sequence acc_mean from clipped acceleration values (1st/99th percentile bounds)
- **Range**: Real numbers (typically -500 to +500)
- **Purpose**: Overall acceleration pattern characterization

#### avg_jerk_mean
- **Description**: Session-wide average of individual sequence jerk means
- **Data Type**: Float (pixels/second³)
- **Calculation**: `mean([seq.jerk_mean for seq in all_sequences])`
- **Individual Basis**: Each sequence jerk_mean from clipped jerk values (1st/99th percentile bounds)
- **Range**: Real numbers (typically -5000 to +5000)
- **Purpose**: Overall movement smoothness characterization

#### total_area
- **Description**: Sum of bounding box areas from all sequences
- **Data Type**: Float (pixels²)
- **Calculation**: `sum([seq.drag_area for seq in all_sequences])`
- **Individual Basis**: Each sequence drag_area = `(x_max - x_min) × (y_max - y_min)`
- **Range**: ≥ 0 pixels² (typically 10000-1000000 for active sessions)
- **Purpose**: Measures total spatial coverage and movement extent

#### shortest_drag_id
- **Description**: Sequence ID of the drag with minimum path length
- **Data Type**: Integer or None
- **Calculation**: `min(drag_sequences, key=lambda x: x.path_length).sequence_id`
- **Individual Basis**: Filters sequences where sequence_type == "Drag", compares path_length values
- **Range**: Valid sequence ID or None if no drags
- **Purpose**: Identifies minimal drawing movements

#### longest_drag_id
- **Description**: Sequence ID of the drag with maximum path length
- **Data Type**: Integer or None
- **Calculation**: `max(drag_sequences, key=lambda x: x.path_length).sequence_id`
- **Individual Basis**: Filters sequences where sequence_type == "Drag", compares path_length values
- **Range**: Valid sequence ID or None if no drags
- **Purpose**: Identifies most extensive drawing movements

### BEHAVIORAL PATTERNS

#### sequence_type_transition_rate
- **Description**: Proportion of sequences that differ in type from the previous sequence
- **Data Type**: Float (proportion)
- **Calculation**: `transitions / total_sequences` where transitions = count of type changes
- **Individual Basis**: Compares sequence_type[i] with sequence_type[i-1] for consecutive sequences
- **Range**: 0.0 to 1.0 (0.0 = no type changes, 1.0 = alternating every sequence)
- **Purpose**: Measures behavioral consistency vs. exploration

#### color_switch_frequency
- **Description**: Total number of color changes between consecutive sequences
- **Data Type**: Integer
- **Calculation**: Count of times `seq[i].start_color != seq[i-1].start_color`
- **Individual Basis**: Each sequence start_color = `events[0]['color']`
- **Range**: ≥ 0 (0 = used same color throughout, higher = more color switching)
- **Purpose**: Measures color exploration and preference consistency

#### repetitive_actions
- **Description**: Count of sequences in same zone as previous sequence within 2-second window
- **Data Type**: Integer
- **Calculation**: Count where `seq[i].primary_zone == seq[i-1].primary_zone AND time_diff <= 2.0`
- **Individual Basis**: primary_zone = `events[0]['zone']`, time_diff = `seq[i].start_time - seq[i-1].start_time`
- **Range**: ≥ 0 (higher values indicate more repetitive spatial behavior)
- **Purpose**: Identifies repetitive or perseverative behaviors

### PREFERENCES AND HABITS

#### most_frequent_zone
- **Description**: Spatial zone that appears most often across all touch events
- **Data Type**: String (categorical)
- **Calculation**: `mode([event['zone'] for event in all_events])`
- **Individual Basis**: Each event contributes its zone value to frequency count
- **Values**: Zone names (e.g., "Zone1", "Outside")
- **Purpose**: Identifies spatial preferences and targeting patterns

#### most_frequent_color
- **Description**: Color that appears most often across all touch events
- **Data Type**: String (categorical)
- **Calculation**: `mode([event['color'] for event in all_events])`
- **Individual Basis**: Each event contributes its color value to frequency count
- **Values**: Color names (e.g., "RedDefault", "Blue")
- **Purpose**: Identifies color preferences and usage patterns

### TASK PERFORMANCE

#### final_completion_percentage
- **Description**: Task completion percentage at the end of the session
- **Data Type**: Float (percentage)
- **Calculation**: `all_events[-1]['completionPerc']` from chronologically last event
- **Individual Basis**: Each event has a completionPerc value tracking cumulative progress
- **Range**: 0.0 to 100.0 (percentage of coloring task completed)
- **Purpose**: Measures task completion and goal achievement

#### completion_efficiency
- **Description**: Rate of progress per unit time
- **Data Type**: Float (percentage/second)
- **Calculation**: `(final_completion - initial_completion) / total_time_spent`
- **Individual Basis**: Uses first and last event completion percentages
- **Range**: ≥ 0 (higher values indicate faster progress)
- **Purpose**: Measures task efficiency and productivity

### INTERACTION TYPE COUNTS

#### drag_count
- **Description**: Total number of sequences classified as "Drag"
- **Data Type**: Integer
- **Calculation**: `count([seq for seq in all_sequences if seq.sequence_type == "Drag"])`
- **Individual Basis**: Each sequence classified using path_length ≥ 15px threshold
- **Range**: ≥ 0 (typically majority of sequences in drawing tasks)
- **Purpose**: Quantifies drawing/painting activity level

#### tap_count
- **Description**: Total number of sequences classified as "Tap"
- **Data Type**: Integer
- **Calculation**: `count([seq for seq in all_sequences if seq.sequence_type == "Tap"])`
- **Individual Basis**: Each sequence classified using duration < 0.25s AND path_length < 15px
- **Range**: ≥ 0 (typically fewer than drags)
- **Purpose**: Quantifies quick selection/targeting behaviors

#### hold_count
- **Description**: Total number of sequences classified as "Hold"
- **Data Type**: Integer
- **Calculation**: `count([seq for seq in all_sequences if seq.sequence_type == "Hold"])`
- **Individual Basis**: Each sequence classified using duration ≥ 0.25s AND path_length < 15px
- **Range**: ≥ 0 (typically least common interaction type)
- **Purpose**: Quantifies sustained attention/pressure behaviors

### MOTOR CONTROL QUALITY

#### average_path_length_drag
- **Description**: Mean path length across all drag sequences
- **Data Type**: Float (pixels)
- **Calculation**: `mean([seq.path_length for seq in drag_sequences])`
- **Individual Basis**: Each drag path_length = sum of Euclidean distances between smoothed coordinates
- **Range**: ≥ 0 pixels (typically 100-10000 for drawing movements)
- **Purpose**: Measures typical drawing movement extent

#### average_straightness_drag
- **Description**: Mean path straightness across all drag sequences
- **Data Type**: Float (ratio)
- **Calculation**: `mean([seq.path_straightness for seq in drag_sequences])`
- **Individual Basis**: Each drag path_straightness = direct_distance / path_length
- **Range**: 0.0 to 1.0 (1.0 = perfectly straight, lower = more curved)
- **Purpose**: Measures drawing movement efficiency and control

#### erratic_drags_count
- **Description**: Number of drags exceeding 75th percentile thresholds for variability
- **Data Type**: Integer
- **Calculation**: Count drags where `speed_variability > p75_speed OR direction_changes > p75_direction`
- **Individual Basis**: Each drag contributes speed_variability and direction_changes values
- **Threshold**: Dynamic 75th percentile calculated from all drags in session
- **Range**: ≥ 0 (higher values indicate more erratic movements)
- **Purpose**: Identifies poor motor control or difficulty episodes

### PRECISION AND ATTENTION

#### average_tap_duration
- **Description**: Mean duration across all tap sequences
- **Data Type**: Float (seconds)
- **Calculation**: `mean([seq.duration for seq in tap_sequences])`
- **Individual Basis**: Each tap duration = `events[-1]['time'] - events[0]['time']`
- **Range**: > 0 seconds (typically 0.01-0.25 seconds by definition)
- **Purpose**: Measures quick touch precision and motor control

#### proportion_of_taps_in_valid_zones
- **Description**: Fraction of taps that landed in valid coloring areas
- **Data Type**: Float (proportion)
- **Calculation**: `valid_taps / total_taps` where valid = not in ["Outside", "Super outside"]
- **Individual Basis**: Each tap primary_zone = `events[0]['zone']`
- **Range**: 0.0 to 1.0 (1.0 = perfect tap accuracy)
- **Purpose**: Measures targeting precision and spatial accuracy

#### average_hold_duration
- **Description**: Mean duration across all hold sequences
- **Data Type**: Float (seconds)
- **Calculation**: `mean([seq.duration for seq in hold_sequences])`
- **Individual Basis**: Each hold duration = `events[-1]['time'] - events[0]['time']`
- **Range**: ≥ 0.25 seconds (by classification definition, typically 0.25-10 seconds)
- **Purpose**: Measures sustained attention and pressure control

---

## DATA QUALITY AND VALIDATION

### Stabilization Techniques Applied

1. **Coordinate Smoothing**: 3-point rolling window on x,y coordinates
2. **Time Threshold Filtering**: 1ms minimum intervals for kinematic calculations
3. **Quantile-Based Clipping**: 1st/99th percentile bounds for acceleration and jerk
4. **Spatial Outlier Removal**: Coordinates > 2000px filtered out
5. **Duplicate Timestamp Removal**: Ensures temporal uniqueness

### Validation Criteria

- **Minimum Session Requirements**: ≥5 sequences AND ≥10 seconds duration
- **Numerical Stability**: All calculations protected against division by zero
- **Feature Completeness**: All 53 features reliably extracted for valid sessions
- **Data Integrity**: Zero NaN or infinity values in final output

### Error Handling

- **Graceful Degradation**: Invalid sequences skipped, processing continues
- **Defensive Programming**: Extensive use of .get() and conditional checks
- **Robust Aggregation**: Statistical functions handle empty lists appropriately

---

## CLINICAL AND RESEARCH APPLICATIONS

### Motor Assessment Applications
- **Velocity/Acceleration/Jerk**: Quantify movement quality and neuromotor control
- **Path Straightness**: Assess movement planning and execution efficiency
- **Speed Variability**: Detect tremor, hesitation, or control difficulties

### Cognitive Assessment Applications
- **Sequence Transitions**: Measure cognitive flexibility and planning
- **Pause Patterns**: Assess processing time and decision-making
- **Repetitive Actions**: Identify perseverative or compulsive behaviors

### Behavioral Pattern Analysis
- **Zone/Color Preferences**: Understand spatial and aesthetic preferences
- **Completion Metrics**: Track task engagement and goal-directed behavior
- **Error Rates**: Assess attention and spatial accuracy

This comprehensive feature set provides a robust foundation for digital therapeutics, research studies, and clinical assessments requiring quantitative behavioral analysis.