#!/usr/bin/env python3
"""
Simplified Feature Validation Tool for ColoringFeatureExtractor
Interactive validation with feature buckets and HTML plots
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.subplots as sp
from plotly.offline import plot
from pathlib import Path
from typing import List, Dict, Tuple
import sys
import os

# Import the ColoringFeatureExtractor
try:
    from coloring_feature_extractor import ColoringFeatureExtractor
except ImportError:
    print("❌ Error: coloring_feature_extractor.py not found!")
    print("Please ensure the file is in the same directory.")
    sys.exit(1)

class SimpleFeatureValidator:
    """Simplified feature validation with organized buckets"""
    
    # Feature organization into logical buckets
    FEATURE_BUCKETS = {
        'spatial': {
            'name': 'Spatial Features',
            'features': ['path_length', 'drag_area', 'path_straightness', 'direction_changes'],
            'description': 'Path geometry and spatial characteristics'
        },
        'temporal': {
            'name': 'Temporal Features', 
            'features': ['duration', 'start_time', 'end_time'],
            'description': 'Time-based sequence characteristics'
        },
        'kinematic': {
            'name': 'Kinematic Features',
            'features': ['velocity_mean', 'velocity_median', 'avg_speed', 'speed_variability', 
                        'acc_mean', 'acc_median', 'jerk_mean', 'jerk_median'],
            'description': 'Motion dynamics and movement patterns'
        },
        'behavioral': {
            'name': 'Behavioral Features',
            'features': ['sequence_type', 'zone_crossings', 'zone_consistency', 'error_rate'],
            'description': 'User behavior and interaction patterns'
        },
        'identification': {
            'name': 'Identification Features',
            'features': ['sequence_id', 'start_color'],
            'description': 'Sequence identification and metadata'
        }
    }
    
    def __init__(self):
        self.extractor = ColoringFeatureExtractor()
    
    def get_available_files(self) -> List[Path]:
        """Get all JSON files in raw_data directory"""
        raw_data_dir = Path('raw_data')
        if not raw_data_dir.exists():
            return []
        return list(raw_data_dir.glob('*.json'))
    
    def load_json_data(self, filepath: Path) -> Dict:
        """Load and parse JSON data"""
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            # Extract touch data based on expected structure
            if 'json' in data and 'touchData' in data['json']:
                return data['json']['touchData']
            elif 'touchData' in data:
                return data['touchData']
            else:
                print(f"⚠️  Unexpected JSON structure in {filepath}")
                return {}
                
        except Exception as e:
            print(f"❌ Error loading {filepath}: {e}")
            return {}
    
    def calculate_raw_features(self, events: List[Dict], sequence_id: int) -> Dict:
        """Calculate features directly from raw events for comparison"""
        if len(events) < 2:
            return {}
        
        df = pd.DataFrame(events)
        
        # Basic calculations
        features = {
            'sequence_id': sequence_id,
            'duration': (df['time'].max() - df['time'].min()) / 1000.0,  # Convert to seconds
            'start_time': df['time'].min() / 1000.0,
            'end_time': df['time'].max() / 1000.0,
        }
        
        # Path length calculation
        if len(df) > 1:
            dx = df['x'].diff().fillna(0)
            dy = df['y'].diff().fillna(0)
            distances = np.sqrt(dx**2 + dy**2)
            features['path_length'] = distances.sum()
        else:
            features['path_length'] = 0
        
        # Direction changes
        if len(df) > 2:
            angles = np.arctan2(dy[1:], dx[1:])
            angle_diffs = np.abs(np.diff(angles))
            features['direction_changes'] = np.sum(angle_diffs > np.pi/4)  # Significant direction changes
        else:
            features['direction_changes'] = 0
        
        # Velocity calculations
        if len(df) > 1:
            dt = df['time'].diff().fillna(1) / 1000.0  # Convert to seconds
            dt = dt.clip(lower=0.001)  # Avoid division by zero
            velocities = distances / dt
            features['velocity_mean'] = velocities.mean()
            features['velocity_median'] = velocities.median()
            features['avg_speed'] = velocities.mean()  # Same as velocity_mean
            features['speed_variability'] = velocities.std()
        else:
            features.update({
                'velocity_mean': 0, 'velocity_median': 0, 
                'avg_speed': 0, 'speed_variability': 0
            })
        
        # Drag area (bounding box)
        if len(df) > 1:
            x_range = df['x'].max() - df['x'].min()
            y_range = df['y'].max() - df['y'].min()
            features['drag_area'] = x_range * y_range
        else:
            features['drag_area'] = 0
        
        # Path straightness (ratio of direct distance to path length)
        if features['path_length'] > 0:
            direct_distance = np.sqrt((df['x'].iloc[-1] - df['x'].iloc[0])**2 + 
                                    (df['y'].iloc[-1] - df['y'].iloc[0])**2)
            features['path_straightness'] = direct_distance / features['path_length']
        else:
            features['path_straightness'] = 0
        
        # Simple sequence type classification
        if features['path_length'] < 10:
            features['sequence_type'] = 'Tap'
        elif features['duration'] < 0.1:
            features['sequence_type'] = 'Tap'
        else:
            features['sequence_type'] = 'Drag'
        
        # Start color (if available)
        features['start_color'] = events[0].get('color', 'Unknown')
        
        # Placeholder values for features that require more complex calculations
        features.update({
            'acc_mean': 0, 'acc_median': 0, 'jerk_mean': 0, 'jerk_median': 0,
            'zone_crossings': 0, 'zone_consistency': 0, 'error_rate': 0
        })
        
        return features
    
    def get_processed_features(self, events: List[Dict], sequence_id: int) -> Dict:
        """Get features from ColoringFeatureExtractor"""
        try:
            # Call ColoringFeatureExtractor with both events and sequence_id
            processed_features = self.extractor.calculate_sequence_features(events, sequence_id)
            return processed_features
        except Exception as e:
            print(f"❌ Error extracting features for sequence {sequence_id}: {e}")
            return {}
    
    def compare_features(self, raw_features: Dict, processed_features: Dict, 
                        bucket_features: List[str]) -> pd.DataFrame:
        """Compare raw vs processed features for a specific bucket"""
        comparison_data = []
        
        for feature in bucket_features:
            raw_val = raw_features.get(feature, 'N/A')
            proc_val = processed_features.get(feature, 'N/A')
            
            # Calculate difference for numeric features
            if isinstance(raw_val, (int, float)) and isinstance(proc_val, (int, float)):
                diff = abs(proc_val - raw_val)
                pct_diff = (diff / raw_val * 100) if raw_val != 0 else 0
                accuracy = "✅ Good" if pct_diff < 5 else "⚠️ Check" if pct_diff < 15 else "❌ Poor"
            else:
                diff = "N/A"
                pct_diff = "N/A"
                accuracy = "✅ Match" if str(raw_val) == str(proc_val) else "❌ Diff"
            
            comparison_data.append({
                'Feature': feature,
                'Raw Value': raw_val,
                'Processed Value': proc_val,
                'Difference': diff,
                'Accuracy': accuracy
            })
        
        return pd.DataFrame(comparison_data)

    def calculate_path_statistics(self, raw_events: List[Dict], processed_events: List[Dict]) -> Dict:
        """Calculate basic statistics comparing raw vs processed paths"""
        try:
            # Calculate raw path length
            raw_length = 0
            for i in range(1, len(raw_events)):
                dx = raw_events[i]['x'] - raw_events[i-1]['x']
                dy = raw_events[i]['y'] - raw_events[i-1]['y']
                raw_length += (dx**2 + dy**2)**0.5

            # Calculate processed path length
            processed_length = 0
            for i in range(1, len(processed_events)):
                dx = processed_events[i]['x'] - processed_events[i-1]['x']
                dy = processed_events[i]['y'] - processed_events[i-1]['y']
                processed_length += (dx**2 + dy**2)**0.5

            # Calculate percentage change
            length_change = ((processed_length - raw_length) / raw_length * 100) if raw_length > 0 else 0

            return {
                'raw_length': raw_length,
                'processed_length': processed_length,
                'length_change': length_change,
                'points_removed': len(raw_events) - len(processed_events)
            }
        except Exception as e:
            return {
                'raw_length': 0,
                'processed_length': 0,
                'length_change': 0,
                'points_removed': 0
            }

    def get_processed_path_data(self, events: List[Dict], sequence_id: int) -> List[Dict]:
        """Get processed path data from ColoringFeatureExtractor"""
        try:
            # Use the extractor's cleaning process
            cleaned_events = self.extractor.clean_sequence_events(events)

            # Apply the same smoothing that happens in _calculate_kinematics
            if len(cleaned_events) < 2:
                return cleaned_events

            # Convert to DataFrame and apply smoothing like in the extractor
            import pandas as pd
            df = pd.DataFrame(cleaned_events)

            # Apply 3-point rolling window smoothing (same as in calculate_sequence_features)
            df['x'] = df['x'].rolling(window=3, min_periods=1, center=True).mean()
            df['y'] = df['y'].rolling(window=3, min_periods=1, center=True).mean()

            # Convert back to list of dictionaries
            smoothed_events = df.to_dict('records')

            return smoothed_events
        except Exception as e:
            print(f"Warning: Could not get processed path data: {e}")
            return events  # Return original events as fallback

    def create_individual_sequence_plot(self, seq_data: Dict, bucket_name: str,
                                       bucket_info: Dict, output_file: str, base_name: str = None) -> str:
        """Create individual sequence validation plot with side-by-side path visualization"""
        sequence_id = seq_data['sequence_id']
        events = seq_data['events']

        # Get processed path data
        processed_events = self.get_processed_path_data(events, sequence_id)

        # Create 2x2 grid layout for better visual clarity
        fig = sp.make_subplots(
            rows=2, cols=2,
            subplot_titles=['Raw Touch Path', 'Processed Touch Path', 'Overlay Comparison', ''],
            vertical_spacing=0.12,
            horizontal_spacing=0.08
        )

        # Extract path coordinates
        raw_x = [event.get('x', 0) for event in events]
        raw_y = [event.get('y', 0) for event in events]
        processed_x = [event.get('x', 0) for event in processed_events]
        processed_y = [event.get('y', 0) for event in processed_events]

        # Add raw path to left subplot
        fig.add_trace(
            go.Scatter(
                x=raw_x, y=raw_y,
                mode='lines+markers',
                name='Raw Path',
                line=dict(color='#2E86AB', width=2, dash='dot'),  # Professional blue
                marker=dict(size=3, color='#A8DADC'),  # Light blue-gray
                showlegend=False
            ),
            row=1, col=1
        )

        # Add processed path to top-right subplot
        fig.add_trace(
            go.Scatter(
                x=processed_x, y=processed_y,
                mode='lines+markers',
                name='Processed Path',
                line=dict(color='#E63946', width=2),  # Professional red
                marker=dict(size=3, color='#F1FAEE'),  # Light cream
                showlegend=False
            ),
            row=1, col=2
        )

        # Add start and end markers for raw path (left subplot)
        if raw_x and raw_y:
            # Raw start point
            fig.add_trace(
                go.Scatter(
                    x=[raw_x[0]], y=[raw_y[0]],
                    mode='markers',
                    name='Start',
                    marker=dict(size=12, color='#457B9D', symbol='circle'),  # Professional teal
                    showlegend=False
                ),
                row=1, col=1
            )

            # Raw end point
            fig.add_trace(
                go.Scatter(
                    x=[raw_x[-1]], y=[raw_y[-1]],
                    mode='markers',
                    name='End',
                    marker=dict(size=12, color='#1D3557', symbol='square'),  # Professional navy
                    showlegend=False
                ),
                row=1, col=1
            )

        # Add start and end markers for processed path (right subplot)
        if processed_x and processed_y:
            # Processed start point
            fig.add_trace(
                go.Scatter(
                    x=[processed_x[0]], y=[processed_y[0]],
                    mode='markers',
                    name='Start',
                    marker=dict(size=12, color='#457B9D', symbol='circle'),  # Professional teal
                    showlegend=False
                ),
                row=1, col=2
            )

            # Processed end point
            fig.add_trace(
                go.Scatter(
                    x=[processed_x[-1]], y=[processed_y[-1]],
                    mode='markers',
                    name='End',
                    marker=dict(size=12, color='#1D3557', symbol='square'),  # Professional navy
                    showlegend=False
                ),
                row=1, col=2
            )

        # Add overlay comparison to bottom-left subplot
        # Raw path overlay
        fig.add_trace(
            go.Scatter(
                x=raw_x, y=raw_y,
                mode='lines+markers',
                name='Raw Path',
                line=dict(color='#2E86AB', width=2, dash='dot'),
                marker=dict(size=3, color='#A8DADC'),
                showlegend=True
            ),
            row=2, col=1
        )

        # Processed path overlay
        fig.add_trace(
            go.Scatter(
                x=processed_x, y=processed_y,
                mode='lines+markers',
                name='Processed Path',
                line=dict(color='#E63946', width=2),
                marker=dict(size=3, color='#F1FAEE'),
                showlegend=True
            ),
            row=2, col=1
        )

        # Add start and end markers for overlay comparison
        if raw_x and raw_y:
            # Raw start point (overlay)
            fig.add_trace(
                go.Scatter(
                    x=[raw_x[0]], y=[raw_y[0]],
                    mode='markers',
                    name='Raw Start',
                    marker=dict(size=10, color='#457B9D', symbol='circle'),
                    showlegend=True
                ),
                row=2, col=1
            )

            # Raw end point (overlay)
            fig.add_trace(
                go.Scatter(
                    x=[raw_x[-1]], y=[raw_y[-1]],
                    mode='markers',
                    name='Raw End',
                    marker=dict(size=10, color='#1D3557', symbol='square'),
                    showlegend=True
                ),
                row=2, col=1
            )

        if processed_x and processed_y:
            # Processed start point (overlay)
            fig.add_trace(
                go.Scatter(
                    x=[processed_x[0]], y=[processed_y[0]],
                    mode='markers',
                    name='Processed Start',
                    marker=dict(size=8, color='#E63946', symbol='circle-open'),
                    showlegend=True
                ),
                row=2, col=1
            )

            # Processed end point (overlay)
            fig.add_trace(
                go.Scatter(
                    x=[processed_x[-1]], y=[processed_y[-1]],
                    mode='markers',
                    name='Processed End',
                    marker=dict(size=8, color='#E63946', symbol='square-open'),
                    showlegend=True
                ),
                row=2, col=1
            )

        # Calculate path statistics for display
        path_stats = self.calculate_path_statistics(events, processed_events)

        # Update layout with filename and sequence info
        title_prefix = f"{base_name} - " if base_name else ""
        fig.update_layout(
            title=f"{title_prefix}Sequence {sequence_id}<br><sub>Raw Events: {len(events)} | Processed Events: {len(processed_events)} | Path Length Change: {path_stats['length_change']:.1f}%</sub>",
            height=750,  # Increased height for 2x2 grid
            width=1200,  # Maintained width for good visibility
            font=dict(size=12),
            legend=dict(
                x=0.52,  # Position legend to the right of the overlay subplot
                y=0.48,  # Center vertically for bottom row
                bgcolor='rgba(255,255,255,0.9)',
                bordercolor='rgba(0,0,0,0.3)',
                borderwidth=1
            )
        )

        # Update axes for 2x2 grid layout
        # Top row: Raw (1,1) and Processed (1,2)
        fig.update_xaxes(title_text="X Coordinate (pixels)", row=1, col=1)
        fig.update_yaxes(title_text="Y Coordinate (pixels)", row=1, col=1, autorange="reversed")
        fig.update_xaxes(title_text="X Coordinate (pixels)", row=1, col=2)
        fig.update_yaxes(title_text="Y Coordinate (pixels)", row=1, col=2, autorange="reversed")

        # Bottom row: Overlay (2,1) and Summary (2,2)
        fig.update_xaxes(title_text="X Coordinate (pixels)", row=2, col=1)
        fig.update_yaxes(title_text="Y Coordinate (pixels)", row=2, col=1, autorange="reversed")

        # Calculate medical evaluation metrics
        events_removed = len(events) - len(processed_events)
        length_change_pct = abs(path_stats['length_change'])
        smoothing_level = 'High' if events_removed > len(events) * 0.1 else 'Moderate' if events_removed > 0 else 'Minimal'

        # Medical evaluation assessment
        if length_change_pct < 5 and events_removed < len(events) * 0.05:
            clinical_suitability = "Excellent for clinical use"
            validation_required = "No"
            smoothing_impact = "Negligible - preserves behavioral patterns"
        elif length_change_pct < 10 and events_removed < len(events) * 0.1:
            clinical_suitability = "Suitable with documentation"
            validation_required = "Recommended"
            smoothing_impact = "Minimal - acceptable for most analyses"
        else:
            clinical_suitability = "Requires clinical review"
            validation_required = "Yes - mandatory"
            smoothing_impact = "Significant - may affect behavioral metrics"

        # Add summary statistics panel to bottom-right (2,2)
        summary_text = f"""
        <b>Path Statistics Summary</b><br><br>
        <b>Raw Data:</b><br>
        • Events: {len(events)}<br>
        • Path Length: {path_stats.get('raw_length', 0):.1f}px<br>
        • Duration: {events[-1].get('time', 0) - events[0].get('time', 0):.2f}s<br><br>
        <b>Processed Data:</b><br>
        • Events: {len(processed_events)}<br>
        • Path Length: {path_stats.get('processed_length', 0):.1f}px<br>
        • Length Change: {path_stats['length_change']:.1f}%<br><br>
        <b>Processing Effects:</b><br>
        • Events Removed: {events_removed}<br>
        • Smoothing Level: {smoothing_level}<br>
        • Data Quality: {'Good' if length_change_pct < 10 else 'Check Required'}<br><br>
        <b>Clinical Assessment:</b><br>
        • Smoothing Impact: {smoothing_impact}<br>
        • Clinical Suitability: {clinical_suitability}<br>
        • Validation Required: {validation_required}<br><br>
        <b>Note:</b> Smoothing effects should be carefully<br>
        evaluated when data is used for medical/behavioral<br>
        assessment. Threshold: &lt;5% change optimal for<br>
        clinical validation, &lt;10% acceptable with review.
        """

        fig.add_annotation(
            text=summary_text,
            x=0.5, y=0.5,
            xref="x4", yref="y4",  # Reference to subplot (2,2)
            showarrow=False,
            font=dict(size=11, family="monospace"),
            bgcolor="rgba(248,249,250,0.8)",
            bordercolor="rgba(0,0,0,0.1)",
            borderwidth=1,
            align="left"
        )

        # Hide axes for summary panel
        fig.update_xaxes(visible=False, row=2, col=2)
        fig.update_yaxes(visible=False, row=2, col=2)

        # Save as HTML
        plot(fig, filename=output_file, auto_open=False)
        return output_file

    def analyze_path_differences(self, sequences_data: List[Dict], output_prefix: str = "path_analysis") -> Dict:
        """
        Comprehensive analysis of differences between raw and processed touch paths

        Args:
            sequences_data: List of sequence data dictionaries
            output_prefix: Prefix for output files

        Returns:
            Dictionary containing analysis results and statistics
        """
        print("🔍 Analyzing path differences across all sequences...")

        analysis_results = {
            'sequence_metrics': [],
            'summary_stats': {},
            'outliers': [],
            'processing_effects': {}
        }

        # Analyze each sequence
        for seq_data in sequences_data:
            sequence_id = seq_data['sequence_id']
            raw_events = seq_data['events']

            try:
                # Get processed events
                processed_events = self.get_processed_path_data(raw_events, sequence_id)

                # Calculate comprehensive metrics
                metrics = self._calculate_comprehensive_metrics(raw_events, processed_events, sequence_id)
                analysis_results['sequence_metrics'].append(metrics)

            except Exception as e:
                print(f"   ⚠️  Error analyzing sequence {sequence_id}: {e}")

        # Calculate summary statistics
        analysis_results['summary_stats'] = self._calculate_summary_statistics(analysis_results['sequence_metrics'])

        # Identify outliers
        analysis_results['outliers'] = self._identify_outliers(analysis_results['sequence_metrics'])

        # Analyze processing effects
        analysis_results['processing_effects'] = self._analyze_processing_effects(analysis_results['sequence_metrics'])

        # Generate reports
        self._generate_analysis_reports(analysis_results, output_prefix)

        return analysis_results

    def _calculate_comprehensive_metrics(self, raw_events: List[Dict], processed_events: List[Dict], sequence_id: int) -> Dict:
        """Calculate comprehensive metrics for a single sequence"""
        import numpy as np

        metrics = {
            'sequence_id': sequence_id,
            'raw_point_count': len(raw_events),
            'processed_point_count': len(processed_events),
            'points_removed': len(raw_events) - len(processed_events),
            'removal_percentage': ((len(raw_events) - len(processed_events)) / len(raw_events) * 100) if raw_events else 0
        }

        if len(raw_events) < 2 or len(processed_events) < 2:
            return metrics

        # Extract coordinates
        raw_x = np.array([e.get('x', 0) for e in raw_events])
        raw_y = np.array([e.get('y', 0) for e in raw_events])
        proc_x = np.array([e.get('x', 0) for e in processed_events])
        proc_y = np.array([e.get('y', 0) for e in processed_events])

        # Path length calculations
        raw_distances = np.sqrt(np.diff(raw_x)**2 + np.diff(raw_y)**2)
        proc_distances = np.sqrt(np.diff(proc_x)**2 + np.diff(proc_y)**2)

        metrics['raw_path_length'] = np.sum(raw_distances)
        metrics['processed_path_length'] = np.sum(proc_distances)
        metrics['path_length_change_pct'] = ((metrics['processed_path_length'] - metrics['raw_path_length']) / metrics['raw_path_length'] * 100) if metrics['raw_path_length'] > 0 else 0

        # Smoothing effect analysis
        raw_velocity_changes = np.abs(np.diff(raw_distances))
        proc_velocity_changes = np.abs(np.diff(proc_distances))

        metrics['raw_velocity_variance'] = np.var(raw_distances) if len(raw_distances) > 1 else 0
        metrics['processed_velocity_variance'] = np.var(proc_distances) if len(proc_distances) > 1 else 0
        metrics['variance_reduction_pct'] = ((metrics['raw_velocity_variance'] - metrics['processed_velocity_variance']) / metrics['raw_velocity_variance'] * 100) if metrics['raw_velocity_variance'] > 0 else 0

        # Spatial bounds analysis
        metrics['raw_width'] = np.max(raw_x) - np.min(raw_x)
        metrics['raw_height'] = np.max(raw_y) - np.min(raw_y)
        metrics['processed_width'] = np.max(proc_x) - np.min(proc_x)
        metrics['processed_height'] = np.max(proc_y) - np.min(proc_y)

        metrics['width_change_pct'] = ((metrics['processed_width'] - metrics['raw_width']) / metrics['raw_width'] * 100) if metrics['raw_width'] > 0 else 0
        metrics['height_change_pct'] = ((metrics['processed_height'] - metrics['raw_height']) / metrics['raw_height'] * 100) if metrics['raw_height'] > 0 else 0

        # Coordinate displacement analysis (for sequences with same point count)
        if len(raw_events) == len(processed_events):
            displacements = np.sqrt((raw_x - proc_x)**2 + (raw_y - proc_y)**2)
            metrics['mean_displacement'] = np.mean(displacements)
            metrics['max_displacement'] = np.max(displacements)
            metrics['displacement_std'] = np.std(displacements)
        else:
            metrics['mean_displacement'] = None
            metrics['max_displacement'] = None
            metrics['displacement_std'] = None

        # Temporal analysis
        raw_times = [e.get('time', 0) for e in raw_events]
        proc_times = [e.get('time', 0) for e in processed_events]

        if len(raw_times) > 1:
            raw_intervals = np.diff(raw_times)
            metrics['raw_time_span'] = max(raw_times) - min(raw_times)
            metrics['raw_avg_interval'] = np.mean(raw_intervals)
            metrics['raw_interval_std'] = np.std(raw_intervals)

        if len(proc_times) > 1:
            proc_intervals = np.diff(proc_times)
            metrics['processed_time_span'] = max(proc_times) - min(proc_times)
            metrics['processed_avg_interval'] = np.mean(proc_intervals)
            metrics['processed_interval_std'] = np.std(proc_intervals)

        return metrics

    def _calculate_summary_statistics(self, metrics_list: List[Dict]) -> Dict:
        """Calculate summary statistics across all sequences"""
        import numpy as np

        if not metrics_list:
            return {}

        # Extract numeric metrics
        numeric_fields = [
            'points_removed', 'removal_percentage', 'path_length_change_pct',
            'variance_reduction_pct', 'width_change_pct', 'height_change_pct',
            'mean_displacement', 'max_displacement'
        ]

        summary = {
            'total_sequences': len(metrics_list),
            'sequences_with_removals': sum(1 for m in metrics_list if m.get('points_removed', 0) > 0),
            'total_points_removed': sum(m.get('points_removed', 0) for m in metrics_list),
            'avg_removal_percentage': np.mean([m.get('removal_percentage', 0) for m in metrics_list])
        }

        # Calculate statistics for each numeric field
        for field in numeric_fields:
            values = [m.get(field) for m in metrics_list if m.get(field) is not None]
            if values:
                summary[f'{field}_mean'] = np.mean(values)
                summary[f'{field}_std'] = np.std(values)
                summary[f'{field}_min'] = np.min(values)
                summary[f'{field}_max'] = np.max(values)
                summary[f'{field}_median'] = np.median(values)

        return summary

    def _identify_outliers(self, metrics_list: List[Dict]) -> List[Dict]:
        """Identify sequences with significant processing differences"""
        import numpy as np

        outliers = []

        # Define outlier thresholds
        thresholds = {
            'high_removal': 20,  # >20% points removed
            'high_length_change': 15,  # >15% path length change
            'high_displacement': None  # Will be calculated as 2 std devs above mean
        }

        # Calculate displacement threshold
        displacements = [m.get('mean_displacement') for m in metrics_list if m.get('mean_displacement') is not None]
        if displacements:
            mean_disp = np.mean(displacements)
            std_disp = np.std(displacements)
            thresholds['high_displacement'] = mean_disp + 2 * std_disp

        # Identify outliers
        for metrics in metrics_list:
            outlier_reasons = []

            if metrics.get('removal_percentage', 0) > thresholds['high_removal']:
                outlier_reasons.append(f"High point removal: {metrics['removal_percentage']:.1f}%")

            if abs(metrics.get('path_length_change_pct', 0)) > thresholds['high_length_change']:
                outlier_reasons.append(f"High path length change: {metrics['path_length_change_pct']:.1f}%")

            if (thresholds['high_displacement'] and
                metrics.get('mean_displacement') and
                metrics['mean_displacement'] > thresholds['high_displacement']):
                outlier_reasons.append(f"High coordinate displacement: {metrics['mean_displacement']:.1f}px")

            if outlier_reasons:
                outliers.append({
                    'sequence_id': metrics['sequence_id'],
                    'reasons': outlier_reasons,
                    'metrics': metrics
                })

        return outliers

    def _analyze_processing_effects(self, metrics_list: List[Dict]) -> Dict:
        """Analyze overall processing effects"""
        import numpy as np

        effects = {
            'smoothing_effectiveness': 0,
            'coordinate_stability': 0,
            'path_preservation': 0,
            'overall_quality': 0
        }

        if not metrics_list:
            return effects

        # Smoothing effectiveness (variance reduction)
        variance_reductions = [m.get('variance_reduction_pct', 0) for m in metrics_list if m.get('variance_reduction_pct') is not None]
        if variance_reductions:
            effects['smoothing_effectiveness'] = np.mean([max(0, vr) for vr in variance_reductions])

        # Coordinate stability (low displacement)
        displacements = [m.get('mean_displacement', 0) for m in metrics_list if m.get('mean_displacement') is not None]
        if displacements:
            # Lower displacement = higher stability (invert scale)
            max_disp = max(displacements) if displacements else 1
            effects['coordinate_stability'] = 100 - (np.mean(displacements) / max_disp * 100)

        # Path preservation (minimal length change)
        length_changes = [abs(m.get('path_length_change_pct', 0)) for m in metrics_list]
        if length_changes:
            # Lower change = better preservation
            effects['path_preservation'] = max(0, 100 - np.mean(length_changes))

        # Overall quality score
        effects['overall_quality'] = np.mean([
            effects['smoothing_effectiveness'],
            effects['coordinate_stability'],
            effects['path_preservation']
        ])

        return effects

    def create_dashboard_html(self, sequences_data: List[Dict], bucket_name: str,
                             bucket_info: Dict, base_filename: str) -> str:
        """Create main dashboard HTML page"""
        dashboard_file = f"validation_{base_filename}_{bucket_name}_dashboard.html"

        # Calculate overall statistics
        total_sequences = len(sequences_data)
        tap_count = sum(1 for seq in sequences_data if seq['processed_features'].get('sequence_type') == 'Tap')
        drag_count = total_sequences - tap_count

        # Calculate feature accuracies
        feature_accuracies = {}
        for feature in bucket_info['features']:
            good_count = 0
            total_count = 0

            for seq_data in sequences_data:
                raw_val = seq_data['raw_features'].get(feature, 0)
                proc_val = seq_data['processed_features'].get(feature, 0)

                if isinstance(raw_val, (int, float)) and isinstance(proc_val, (int, float)):
                    total_count += 1
                    if raw_val != 0:
                        pct_diff = abs(proc_val - raw_val) / raw_val * 100
                        if pct_diff < 5:
                            good_count += 1
                    elif proc_val == 0:
                        good_count += 1

            if total_count > 0:
                accuracy = (good_count / total_count) * 100
                feature_accuracies[feature] = accuracy

        overall_accuracy = np.mean(list(feature_accuracies.values())) if feature_accuracies else 0

        # Generate HTML content
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{base_filename} - {bucket_info['name']} Dashboard</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .accuracy-summary {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}
        .accuracy-summary h3 {{
            margin-top: 0;
            color: #333;
        }}
        .accuracy-item {{
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        .accuracy-item:last-child {{
            border-bottom: none;
        }}
        .accuracy-value {{
            font-weight: bold;
        }}
        .accuracy-good {{ color: #28a745; }}
        .accuracy-warning {{ color: #ffc107; }}
        .accuracy-poor {{ color: #dc3545; }}
        .sequences-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }}
        .sequence-card {{
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.2s;
        }}
        .sequence-card:hover {{
            transform: translateY(-5px);
        }}
        .sequence-header {{
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }}
        .sequence-id {{
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }}
        .sequence-type {{
            display: inline-block;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            margin-top: 5px;
        }}
        .type-tap {{
            background: #e3f2fd;
            color: #1976d2;
        }}
        .type-drag {{
            background: #f3e5f5;
            color: #7b1fa2;
        }}
        .sequence-body {{
            padding: 15px;
        }}
        .sequence-metrics {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }}
        .metric {{
            text-align: center;
        }}
        .metric-value {{
            font-size: 1.1em;
            font-weight: bold;
            color: #667eea;
        }}
        .metric-label {{
            font-size: 0.8em;
            color: #666;
        }}
        .view-button {{
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            cursor: pointer;
            transition: opacity 0.2s;
        }}
        .view-button:hover {{
            opacity: 0.9;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 {base_filename} - Dashboard</h1>
        <p>{bucket_info['name']} - {bucket_info['description']}</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{total_sequences}</div>
            <div class="stat-label">Total Sequences</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{tap_count}</div>
            <div class="stat-label">Tap Sequences</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{drag_count}</div>
            <div class="stat-label">Drag Sequences</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{overall_accuracy:.1f}%</div>
            <div class="stat-label">Overall Accuracy</div>
        </div>
    </div>

    <div class="accuracy-summary">
        <h3>📊 Feature Accuracy Summary</h3>"""

        for feature, accuracy in feature_accuracies.items():
            accuracy_class = "accuracy-good" if accuracy >= 80 else "accuracy-warning" if accuracy >= 60 else "accuracy-poor"
            html_content += f"""
        <div class="accuracy-item">
            <span>{feature.replace('_', ' ').title()}</span>
            <span class="accuracy-value {accuracy_class}">{accuracy:.1f}%</span>
        </div>"""

        html_content += """
    </div>

    <div class="sequences-grid">"""

        for seq_data in sequences_data:
            seq_id = seq_data['sequence_id']
            # Use raw data for dashboard display to show original sequence characteristics
            seq_type = seq_data['raw_features'].get('sequence_type', 'Unknown')
            duration = seq_data['raw_features'].get('duration', 0)
            path_length = seq_data['raw_features'].get('path_length', 0)
            event_count = len(seq_data.get('events', []))

            type_class = "type-tap" if seq_type == 'Tap' else "type-drag"
            plot_file = f"validation_{base_filename}_{bucket_name}_seq_{seq_id}.html"

            html_content += f"""
        <div class="sequence-card">
            <div class="sequence-header">
                <div class="sequence-id">Sequence {seq_id}</div>
                <span class="sequence-type {type_class}">{seq_type}</span>
            </div>
            <div class="sequence-body">
                <div class="sequence-metrics">
                    <div class="metric">
                        <div class="metric-value">{event_count}</div>
                        <div class="metric-label">Raw Events</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{duration:.2f}s</div>
                        <div class="metric-label">Duration</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{path_length:.0f}px</div>
                        <div class="metric-label">Path Length</div>
                    </div>
                </div>
                <button class="view-button" onclick="window.open('{plot_file}', '_blank')">
                    📈 View Validation Plot
                </button>
            </div>
        </div>"""

        html_content += """
    </div>

    <script>
        console.log('Feature Validation Dashboard loaded successfully');
    </script>
</body>
</html>"""

        # Save dashboard HTML
        with open(dashboard_file, 'w') as f:
            f.write(html_content)

        return dashboard_file

    def create_static_plot(self, sequences_data: List[Dict], bucket_name: str,
                          bucket_info: Dict, output_file: str) -> str:
        """Create static matplotlib plot for a feature bucket"""
        bucket_features = bucket_info['features']

        # Create subplots
        n_features = len(bucket_features)
        n_cols = min(2, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(12, 6*n_rows))
        if n_features == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        # Extract data
        sequence_ids = [seq_data['sequence_id'] for seq_data in sequences_data]

        for i, feature in enumerate(bucket_features):
            ax = axes[i] if i < len(axes) else axes[-1]

            raw_vals = []
            proc_vals = []

            for seq_data in sequences_data:
                raw_val = seq_data['raw_features'].get(feature, 0)
                proc_val = seq_data['processed_features'].get(feature, 0)

                # Handle non-numeric values
                if not isinstance(raw_val, (int, float)):
                    raw_val = 0
                if not isinstance(proc_val, (int, float)):
                    proc_val = 0

                raw_vals.append(raw_val)
                proc_vals.append(proc_val)

            # Plot
            ax.plot(sequence_ids, raw_vals, 'b--o', label='Raw', markersize=6, alpha=0.7)
            ax.plot(sequence_ids, proc_vals, 'r-s', label='Processed', markersize=6, alpha=0.8)

            ax.set_title(f"{feature.replace('_', ' ').title()}")
            ax.set_xlabel('Sequence ID')
            ax.set_ylabel('Feature Value')
            ax.legend()
            ax.grid(True, alpha=0.3)

        # Hide unused subplots
        for i in range(n_features, len(axes)):
            axes[i].set_visible(False)

        plt.suptitle(f"Feature Validation: {bucket_info['name']}", fontsize=16, fontweight='bold')
        plt.tight_layout()

        # Save
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()

        return output_file

def interactive_file_selection(validator: SimpleFeatureValidator) -> Path:
    """Interactive file selection from raw_data directory"""
    files = validator.get_available_files()

    if not files:
        print("❌ No JSON files found in raw_data/ directory!")
        sys.exit(1)

    print("\n📁 Available JSON files:")
    for i, file in enumerate(files, 1):
        print(f"   {i}. {file.name}")

    while True:
        try:
            choice = input(f"\nSelect file (1-{len(files)}): ").strip()
            idx = int(choice) - 1
            if 0 <= idx < len(files):
                return files[idx]
            else:
                print(f"❌ Please enter a number between 1 and {len(files)}")
        except ValueError:
            print("❌ Please enter a valid number")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)

def interactive_bucket_selection(validator: SimpleFeatureValidator) -> Tuple[str, Dict]:
    """Interactive feature bucket selection"""
    buckets = validator.FEATURE_BUCKETS

    print("\n🎯 Feature Buckets Available:")
    bucket_list = list(buckets.items())

    for i, (bucket_key, bucket_info) in enumerate(bucket_list, 1):
        print(f"   {i}. {bucket_info['name']}")
        print(f"      📋 {bucket_info['description']}")
        print(f"      🔧 Features: {', '.join(bucket_info['features'])}")
        print()

    while True:
        try:
            choice = input(f"Select feature bucket (1-{len(bucket_list)}): ").strip()
            idx = int(choice) - 1
            if 0 <= idx < len(bucket_list):
                bucket_key, bucket_info = bucket_list[idx]
                return bucket_key, bucket_info
            else:
                print(f"❌ Please enter a number between 1 and {len(bucket_list)}")
        except ValueError:
            print("❌ Please enter a valid number")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)

def main():
    """Main interactive validation workflow"""
    print("🎯 SIMPLIFIED COLORINGFEATUREEXTRACTOR VALIDATOR")
    print("=" * 60)
    print("Interactive validation with organized feature buckets")
    print()

    # Initialize validator
    validator = SimpleFeatureValidator()

    # Step 1: File selection
    selected_file = interactive_file_selection(validator)
    print(f"✅ Selected: {selected_file.name}")

    # Step 2: Load and process data
    print(f"\n🔍 Loading data from {selected_file.name}...")
    touch_data = validator.load_json_data(selected_file)

    if not touch_data:
        print("❌ No valid touch data found!")
        return

    print(f"📊 Found {len(touch_data)} sequences")

    # Process all sequences
    sequences_data = []
    successful_extractions = 0

    for seq_id, events in touch_data.items():
        if len(events) < 2:
            continue

        print(f"   Processing sequence {seq_id}...", end=" ")

        # Calculate raw features
        raw_features = validator.calculate_raw_features(events, int(seq_id))

        # Get processed features from ColoringFeatureExtractor
        processed_features = validator.get_processed_features(events, int(seq_id))

        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
            successful_extractions += 1
            print("✅")
        else:
            print("❌")

    if not sequences_data:
        print("❌ No sequences could be processed!")
        return

    print(f"\n✅ Successfully processed {successful_extractions} sequences")

    # Step 3: Feature bucket selection
    bucket_key, bucket_info = interactive_bucket_selection(validator)
    print(f"✅ Selected: {bucket_info['name']}")

    # Step 4: Generate validation dashboard and individual plots
    print(f"\n🎨 Generating validation dashboard for {bucket_info['name']}...")

    base_name = selected_file.stem

    # Generate main dashboard
    try:
        dashboard_file = validator.create_dashboard_html(sequences_data, bucket_key, bucket_info, base_name)
        print(f"✅ Main dashboard: {dashboard_file}")
    except Exception as e:
        print(f"⚠️  Dashboard generation failed: {e}")
        dashboard_file = None

    # Generate individual sequence plots
    print(f"🔍 Generating individual sequence plots...")
    individual_plots = []

    for i, seq_data in enumerate(sequences_data):
        seq_id = seq_data['sequence_id']
        plot_file = f"validation_{base_name}_{bucket_key}_seq_{seq_id}.html"

        try:
            output_file = validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file, base_name)
            individual_plots.append(output_file)
            print(f"   ✅ Sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")

    # Generate static PNG summary plot
    png_file = f"validation_{base_name}_{bucket_key}_summary.png"
    try:
        png_output = validator.create_static_plot(sequences_data, bucket_key, bucket_info, png_file)
        print(f"✅ Static summary plot: {png_output}")
    except Exception as e:
        print(f"⚠️  PNG plot generation failed: {e}")
        png_output = None

    # Step 5: Show summary comparison
    print(f"\n📋 VALIDATION SUMMARY for {bucket_info['name']}:")
    print("-" * 50)

    # Create comparison table for the first few sequences
    sample_sequences = sequences_data[:5]  # Show first 5 sequences

    for seq_data in sample_sequences:
        print(f"\n🔍 Sequence {seq_data['sequence_id']}:")
        comparison_df = validator.compare_features(
            seq_data['raw_features'],
            seq_data['processed_features'],
            bucket_info['features']
        )

        for _, row in comparison_df.iterrows():
            print(f"   {row['Feature']:20} | Raw: {str(row['Raw Value']):>10} | Processed: {str(row['Processed Value']):>10} | {row['Accuracy']}")

    # Overall accuracy summary
    print(f"\n📊 OVERALL ACCURACY:")
    feature_accuracies = []

    for feature in bucket_info['features']:
        good_count = 0
        total_count = 0

        for seq_data in sequences_data:
            raw_val = seq_data['raw_features'].get(feature, 0)
            proc_val = seq_data['processed_features'].get(feature, 0)

            if isinstance(raw_val, (int, float)) and isinstance(proc_val, (int, float)):
                total_count += 1
                if raw_val != 0:
                    pct_diff = abs(proc_val - raw_val) / raw_val * 100
                    if pct_diff < 5:  # Within 5% is considered good
                        good_count += 1
                elif proc_val == 0:  # Both are zero
                    good_count += 1

        if total_count > 0:
            accuracy = (good_count / total_count) * 100
            feature_accuracies.append(accuracy)
            print(f"   {feature:20}: {accuracy:5.1f}% accurate ({good_count}/{total_count})")

    if feature_accuracies:
        overall_accuracy = np.mean(feature_accuracies)
        print(f"\n🎯 Overall {bucket_info['name']} Accuracy: {overall_accuracy:.1f}%")

    # Step 6: Open dashboard in browser
    if dashboard_file:
        print(f"\n🌐 Opening validation dashboard in browser...")
        try:
            import webbrowser
            import os
            dashboard_path = os.path.abspath(dashboard_file)
            webbrowser.open(f"file://{dashboard_path}")
            print(f"✅ Dashboard opened: {dashboard_file}")
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print(f"📂 Please manually open: {dashboard_file}")

    print(f"\n🎉 Validation complete! Generated files:")
    if dashboard_file:
        print(f"   🎯 Main Dashboard: {dashboard_file}")
    print(f"   📊 Individual Plots: {len(individual_plots)} sequence-specific HTML files")
    if png_output:
        print(f"   🖼️  Summary Plot: {png_output}")

    print(f"\n📋 Navigation Guide:")
    print(f"   1. Review the main dashboard for overview and statistics")
    print(f"   2. Click 'View Validation Plot' for any sequence to see detailed comparison")
    print(f"   3. Each sequence plot shows raw vs processed feature values")
    print(f"   4. Use the static summary plot for documentation/reports")

if __name__ == "__main__":
    main()
