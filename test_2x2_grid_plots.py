#!/usr/bin/env python3
"""
Test script for the enhanced 2x2 grid path visualization
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def test_2x2_grid_visualization():
    """Test the enhanced individual sequence plots with 2x2 grid layout"""
    print("🎨 Testing 2x2 grid path visualization...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process first few sequences for testing
    sequences_data = []
    for seq_id, events in list(touch_data.items())[:3]:  # Test first 3 sequences
        if len(events) < 2:
            continue
        
        print(f"   Processing sequence {seq_id}...")
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Processed {len(sequences_data)} sequences")
    
    # Test spatial features bucket with 2x2 grid visualization
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    print(f"🎨 Testing 2x2 grid individual plots...")
    
    # Generate 2x2 grid individual plots
    generated_files = []
    for seq_data in sequences_data:
        seq_id = seq_data['sequence_id']
        plot_file = f"grid_2x2_{base_name}_{bucket_key}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(
                seq_data, bucket_key, bucket_info, plot_file, base_name
            )
            generated_files.append(output_file)
            print(f"   ✅ 2x2 grid sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")
            import traceback
            traceback.print_exc()
    
    if generated_files:
        # Open the first 2x2 grid plot
        first_plot = generated_files[0]
        print(f"\n🌐 Opening 2x2 grid plot in browser: {first_plot}")
        try:
            plot_path = os.path.abspath(first_plot)
            webbrowser.open(f"file://{plot_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    print(f"\n🎉 2x2 grid path visualization test complete!")
    print(f"📂 Generated 2x2 grid files:")
    for file in generated_files:
        print(f"   📊 {file}")
    
    print(f"\n📋 2x2 Grid Layout Features:")
    print(f"   🎯 Top-Left: Raw touch path (larger size)")
    print(f"   🔄 Top-Right: Processed touch path (larger size)")
    print(f"   📊 Bottom-Left: Overlay comparison with legend")
    print(f"   📈 Bottom-Right: Summary statistics panel")
    print(f"   🎨 Professional color scheme throughout")
    print(f"   📏 Increased height (750px) for better visibility")
    print(f"   🔍 Interactive zoom/pan capabilities")

def test_summary_statistics_panel():
    """Test the summary statistics panel in the bottom-right"""
    print("\n📊 Testing summary statistics panel...")
    
    validator = SimpleFeatureValidator()
    files = validator.get_available_files()
    
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    touch_data = validator.load_json_data(selected_file)
    
    # Find a sequence with good characteristics for demonstration
    best_sequence = None
    
    for seq_id, events in touch_data.items():
        if 20 <= len(events) <= 80:  # Good range for demonstration
            best_sequence = (seq_id, events)
            break
    
    if not best_sequence:
        # Fallback to any sequence
        best_sequence = list(touch_data.items())[0]
    
    seq_id, events = best_sequence
    print(f"📊 Testing summary panel with sequence {seq_id} ({len(events)} events)")
    
    # Process the sequence
    raw_features = validator.calculate_raw_features(events, int(seq_id))
    processed_features = validator.get_processed_features(events, int(seq_id))
    
    seq_data = {
        'sequence_id': int(seq_id),
        'raw_features': raw_features,
        'processed_features': processed_features,
        'events': events
    }
    
    # Generate 2x2 grid plot with summary panel
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    plot_file = f"summary_test_{base_name}_{bucket_key}_seq_{seq_id}.html"
    
    try:
        output_file = validator.create_individual_sequence_plot(
            seq_data, bucket_key, bucket_info, plot_file, base_name
        )
        print(f"✅ Generated summary panel test: {output_file}")
        
        # Open the plot
        plot_path = os.path.abspath(output_file)
        webbrowser.open(f"file://{plot_path}")
        print(f"🌐 Opened 2x2 grid with summary panel")
        
        # Print sequence details for verification
        processed_events = validator.get_processed_path_data(events, int(seq_id))
        path_stats = validator.calculate_path_statistics(events, processed_events)
        
        print(f"\n📋 Summary Panel Content:")
        print(f"   📏 Raw Events: {len(events)}")
        print(f"   🔄 Processed Events: {len(processed_events)}")
        print(f"   📊 Events Removed: {len(events) - len(processed_events)}")
        print(f"   🎯 Path Length Change: {path_stats['length_change']:.1f}%")
        print(f"   ⏱️  Duration: {events[-1].get('time', 0) - events[0].get('time', 0):.2f}s")
        
    except Exception as e:
        print(f"❌ Failed to generate summary panel test: {e}")
        import traceback
        traceback.print_exc()

def test_layout_comparison():
    """Compare the new 2x2 layout with previous versions"""
    print("\n🔄 Layout Comparison Summary:")
    print("=" * 50)
    
    print("📊 Previous 1x3 Layout Issues:")
    print("   ❌ Individual plots too small for detailed analysis")
    print("   ❌ Three columns compressed visualization space")
    print("   ❌ Overlay comparison lacked prominence")
    print("   ❌ Limited space for additional information")
    
    print("\n✅ New 2x2 Grid Layout Benefits:")
    print("   🎯 Larger individual plots for better detail examination")
    print("   📊 Prominent overlay comparison in dedicated space")
    print("   📈 Summary statistics panel for quick insights")
    print("   🎨 Better utilization of screen real estate")
    print("   🔍 Improved readability of path trajectories")
    print("   📏 Increased height (750px) for better visibility")
    print("   🎯 Professional layout with clear visual hierarchy")

if __name__ == "__main__":
    test_2x2_grid_visualization()
    test_summary_statistics_panel()
    test_layout_comparison()
