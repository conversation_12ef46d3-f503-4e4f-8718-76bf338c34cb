#!/usr/bin/env python3
"""
Test script for the enhanced path visualization functionality
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def test_path_visualization():
    """Test the enhanced individual sequence plots with path visualization"""
    print("🧪 Testing enhanced path visualization...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process first few sequences for testing
    sequences_data = []
    for seq_id, events in list(touch_data.items())[:3]:  # Test first 3 sequences
        if len(events) < 2:
            continue
        
        print(f"   Processing sequence {seq_id}...")
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Processed {len(sequences_data)} sequences")
    
    # Test spatial features bucket with path visualization
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    base_name = selected_file.stem
    
    print(f"🎨 Testing enhanced individual plots with path visualization...")
    
    # Generate enhanced individual plots
    generated_files = []
    for seq_data in sequences_data:
        seq_id = seq_data['sequence_id']
        plot_file = f"enhanced_validation_{base_name}_{bucket_key}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file)
            generated_files.append(output_file)
            print(f"   ✅ Enhanced sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")
            import traceback
            traceback.print_exc()
    
    if generated_files:
        # Open the first enhanced plot
        first_plot = generated_files[0]
        print(f"\n🌐 Opening enhanced plot in browser: {first_plot}")
        try:
            plot_path = os.path.abspath(first_plot)
            webbrowser.open(f"file://{plot_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    print(f"\n🎉 Enhanced path visualization test complete!")
    print(f"📂 Generated enhanced files:")
    for file in generated_files:
        print(f"   📊 {file}")
    
    print(f"\n📋 Enhanced Features:")
    print(f"   🎯 Raw touch path visualization with start/end markers")
    print(f"   🔄 Processed path showing smoothing effects")
    print(f"   📊 Side-by-side path comparison")
    print(f"   📈 Feature value comparisons with accuracy indicators")

if __name__ == "__main__":
    test_path_visualization()
