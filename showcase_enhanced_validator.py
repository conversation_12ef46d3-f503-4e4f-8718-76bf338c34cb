#!/usr/bin/env python3
"""
Comprehensive showcase of the enhanced feature validation tool with path visualization
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def showcase_enhanced_features():
    """Showcase all enhanced features of the validation tool"""
    print("🎯 ENHANCED FEATURE VALIDATION TOOL SHOWCASE")
    print("=" * 60)
    print("🚀 Demonstrating comprehensive dashboard + path visualization")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get available files
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found in raw_data/ directory!")
        return
    
    selected_file = files[0]
    print(f"📁 Using file: {selected_file.name}")
    
    # Load and process data
    print(f"🔍 Loading and processing touch data...")
    touch_data = validator.load_json_data(selected_file)
    
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} total sequences")
    
    # Process all sequences
    sequences_data = []
    successful_extractions = 0
    
    for seq_id, events in touch_data.items():
        if len(events) < 2:
            continue
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
            successful_extractions += 1
    
    print(f"✅ Successfully processed {successful_extractions} sequences")
    
    base_name = selected_file.stem
    
    # Showcase different feature buckets
    showcase_buckets = ['spatial', 'kinematic', 'temporal']
    generated_dashboards = []
    
    for bucket_key in showcase_buckets:
        bucket_info = validator.FEATURE_BUCKETS[bucket_key]
        print(f"\n🎨 Generating {bucket_info['name']} validation...")
        
        try:
            # Generate main dashboard
            dashboard_file = validator.create_dashboard_html(sequences_data, bucket_key, bucket_info, base_name)
            print(f"   ✅ Dashboard: {dashboard_file}")
            
            # Generate enhanced individual sequence plots (first 5 for demo)
            individual_count = 0
            for seq_data in sequences_data[:5]:
                seq_id = seq_data['sequence_id']
                plot_file = f"showcase_{base_name}_{bucket_key}_seq_{seq_id}.html"
                
                try:
                    validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file)
                    individual_count += 1
                except Exception as e:
                    print(f"   ⚠️  Sequence {seq_id} plot failed: {e}")
            
            print(f"   📊 Generated {individual_count} enhanced sequence plots with path visualization")
            generated_dashboards.append((bucket_key, bucket_info['name'], dashboard_file))
            
        except Exception as e:
            print(f"   ❌ {bucket_info['name']} generation failed: {e}")
    
    # Summary of enhanced features
    print(f"\n🎉 ENHANCED VALIDATION SHOWCASE COMPLETE!")
    print(f"📂 Generated {len(generated_dashboards)} feature bucket dashboards")
    
    for bucket_key, bucket_name, dashboard_file in generated_dashboards:
        print(f"   🎯 {bucket_name}: {dashboard_file}")
    
    # Open the spatial dashboard (most visual)
    if generated_dashboards:
        spatial_dashboard = None
        for bucket_key, bucket_name, dashboard_file in generated_dashboards:
            if bucket_key == 'spatial':
                spatial_dashboard = dashboard_file
                break
        
        if spatial_dashboard:
            print(f"\n🌐 Opening Spatial Features dashboard (best for path visualization)...")
            try:
                dashboard_path = os.path.abspath(spatial_dashboard)
                webbrowser.open(f"file://{dashboard_path}")
                print(f"✅ Opened: {spatial_dashboard}")
            except Exception as e:
                print(f"⚠️  Could not open browser: {e}")
    
    # Feature showcase summary
    print(f"\n🚀 ENHANCED FEATURES DEMONSTRATED:")
    print(f"   📊 Main Dashboard Interface:")
    print(f"      • Clean sequence overview with statistics")
    print(f"      • Interactive sequence cards with 'View Plot' buttons")
    print(f"      • Overall accuracy metrics and feature summaries")
    print(f"      • Responsive design with modern styling")
    
    print(f"\n   🎯 Enhanced Individual Sequence Plots:")
    print(f"      • Raw touch path visualization (blue dotted line)")
    print(f"      • Processed path visualization (red solid line)")
    print(f"      • Start/end markers (green circle, red square)")
    print(f"      • Side-by-side coordinate comparison")
    print(f"      • Interactive zoom/pan with hover tooltips")
    
    print(f"\n   📈 Feature Validation Integration:")
    print(f"      • Color-coded accuracy indicators")
    print(f"      • Exact numerical value comparisons")
    print(f"      • Visual correlation with path characteristics")
    print(f"      • Comprehensive validation workflow")
    
    print(f"\n📋 VALIDATION WORKFLOW:")
    print(f"   1. Review main dashboard for overview")
    print(f"   2. Select sequences using 'View Plot' buttons")
    print(f"   3. Inspect raw vs processed touch paths")
    print(f"   4. Verify feature extraction accuracy")
    print(f"   5. Identify any processing issues")
    print(f"   6. Validate that smoothing preserves drawing intent")
    
    print(f"\n🎯 The enhanced tool provides comprehensive visual validation")
    print(f"   of ColoringFeatureExtractor accuracy and path processing!")

def showcase_specific_sequence():
    """Showcase a specific sequence with detailed analysis"""
    print("🔍 DETAILED SEQUENCE ANALYSIS SHOWCASE")
    print("=" * 50)
    
    validator = SimpleFeatureValidator()
    files = validator.get_available_files()
    
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    touch_data = validator.load_json_data(selected_file)
    
    # Find a good sequence for demonstration (longer sequences are more interesting)
    best_sequence = None
    max_events = 0
    
    for seq_id, events in touch_data.items():
        if len(events) > max_events:
            max_events = len(events)
            best_sequence = (seq_id, events)
    
    if not best_sequence:
        print("❌ No suitable sequence found!")
        return
    
    seq_id, events = best_sequence
    print(f"📊 Analyzing sequence {seq_id} with {len(events)} touch events")
    
    # Process the sequence
    raw_features = validator.calculate_raw_features(events, int(seq_id))
    processed_features = validator.get_processed_features(events, int(seq_id))
    
    seq_data = {
        'sequence_id': int(seq_id),
        'raw_features': raw_features,
        'processed_features': processed_features,
        'events': events
    }
    
    # Generate enhanced plot for spatial features
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    base_name = selected_file.stem
    
    plot_file = f"detailed_analysis_{base_name}_{bucket_key}_seq_{seq_id}.html"
    
    try:
        output_file = validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file)
        print(f"✅ Generated detailed analysis: {output_file}")
        
        # Open the plot
        plot_path = os.path.abspath(output_file)
        webbrowser.open(f"file://{plot_path}")
        print(f"🌐 Opened detailed sequence analysis")
        
        # Print sequence details
        print(f"\n📋 Sequence {seq_id} Details:")
        print(f"   📏 Path Length: {raw_features.get('path_length', 0):.1f}px")
        print(f"   ⏱️  Duration: {raw_features.get('duration', 0):.2f}s")
        print(f"   🎯 Type: {processed_features.get('sequence_type', 'Unknown')}")
        print(f"   📊 Touch Events: {len(events)}")
        
    except Exception as e:
        print(f"❌ Failed to generate detailed analysis: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "detailed":
        showcase_specific_sequence()
    else:
        showcase_enhanced_features()
