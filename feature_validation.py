import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from matplotlib.animation import FuncAnimation
import plotly.express as px
import streamlit as st
from pathlib import Path
from typing import List, Dict
import argparse
import logging

# Suppress Matplotlib font debug logs
logging.getLogger('matplotlib.font_manager').setLevel(logging.WARNING)

# Import from your original file (adjust path if needed)
from coloring_feature_extractor import ColoringFeatureExtractor

class FeatureValidation:
    def __init__(self, raw_mode: bool = False):
        self.raw_mode = raw_mode
        self.extractor = ColoringFeatureExtractor()  # Instance of original extractor

    def load_json(self, filepath: str) -> Dict:
        with open(filepath, 'r') as f:
            data = json.load(f)
        return data.get('json', {}).get('touchData', {})

    def flag_abnormalities(self, events: List[Dict]) -> Dict:
        abnormalities = {
            'duplicates': 0,
            'outliers': 0,
            'erratic_spikes': 0
        }
        timestamps = [e['time'] for e in events]
        abnormalities['duplicates'] = len(timestamps) - len(set(timestamps))
        abnormalities['outliers'] = sum(1 for e in events if e.get('x', 0) > 2000 or e.get('y', 0) > 2000)
        
        df = pd.DataFrame(events)
        if len(df) > 1:
            df['delta_time'] = df['time'].diff()
            delta_d = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)
            df['velocity'] = delta_d / df['delta_time'].clip(lower=0.001)
            abnormalities['erratic_spikes'] = sum(abs(df['velocity'].diff()) > 1000)
        
        return abnormalities

    def extract_raw_features(self, events: List[Dict]) -> Dict:
        if len(events) < 2:
            return {}
        
        duration = events[-1]['time'] - events[0]['time']
        path_length = sum(np.sqrt((events[i]['x'] - events[i-1]['x'])**2 + (events[i]['y'] - events[i-1]['y'])**2) for i in range(1, len(events)))
        df = pd.DataFrame(events)
        df['delta_time'] = df['time'].diff()
        delta_d = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)
        df['velocity'] = delta_d / df['delta_time'].clip(lower=0.001)
        velocity_mean = df['velocity'].mean()
        
        direction_changes = 0
        for i in range(1, len(events) - 1):
            p1 = (events[i-1]['x'], events[i-1]['y'])
            p2 = (events[i]['x'], events[i]['y'])
            p3 = (events[i+1]['x'], events[i+1]['y'])
            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])
            mag1 = np.sqrt(v1[0]**2 + v1[1]**2)
            mag2 = np.sqrt(v2[0]**2 + v2[1]**2)
            if mag1 > 0 and mag2 > 0:
                dot = v1[0]*v2[0] + v1[1]*v2[1]
                angle = np.arccos(np.clip(dot / (mag1 * mag2), -1.0, 1.0))
                if angle > np.pi / 6:
                    direction_changes += 1
        
        return {
            'duration': duration,
            'path_length': path_length,
            'velocity_mean': velocity_mean,
            'direction_changes': direction_changes
        }

    def prepare_vis_data(self, events: List[Dict], sequence_id: int) -> Dict:
        raw_events = events.copy()
        abnormalities = self.flag_abnormalities(raw_events)
        
        # Extract features using original extractor (processed/cleaned)
        processed_features = self.extractor.calculate_sequence_features(events, sequence_id) or {}
        
        # Extract minimal raw features for comparison
        raw_features = self.extract_raw_features(raw_events)
        
        return {
            'sequence_id': sequence_id,
            'raw_events': raw_events,
            'raw_features': raw_features,
            'processed_features': processed_features,
            'abnormalities': abnormalities
        }

    def visualize_static(self, vis_data: Dict, save_path: str = None):
        """Enhanced visualization with comprehensive feature validation"""
        raw_df = pd.DataFrame(vis_data['raw_events'])
        raw_features = vis_data['raw_features']
        processed_features = vis_data['processed_features']
        abns = vis_data['abnormalities']

        # Create a comprehensive 2x2 subplot layout
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

        # === TOP LEFT: RAW DATA PATH WITH QUALITY INDICATORS ===
        sns.lineplot(x='x', y='y', data=raw_df, ax=ax1, marker='o', color='blue', label='Raw Path', markersize=4)

        # Highlight start and end points
        ax1.scatter(raw_df.iloc[0]['x'], raw_df.iloc[0]['y'], color='green', s=100, label='Start', marker='s')
        ax1.scatter(raw_df.iloc[-1]['x'], raw_df.iloc[-1]['y'], color='red', s=100, label='End', marker='s')

        # Show outliers and spikes
        outliers = raw_df[(raw_df['x'] > 2000) | (raw_df['y'] > 2000)]
        if not outliers.empty:
            ax1.scatter(outliers['x'], outliers['y'], color='red', label='Outliers', s=50)

        # Velocity spike detection
        try:
            if len(raw_df) > 1:
                if 'velocity' not in raw_df.columns:
                    raw_df['delta_time'] = raw_df['time'].diff()
                    delta_d = np.sqrt(raw_df['x'].diff()**2 + raw_df['y'].diff()**2)
                    raw_df['velocity'] = delta_d / raw_df['delta_time'].clip(lower=0.001)

                velocity_diff = raw_df['velocity'].diff().abs()
                spike_mask = velocity_diff > 1000
                spikes = raw_df[spike_mask]

                if not spikes.empty and 'x' in spikes.columns:
                    ax1.scatter(spikes['x'], spikes['y'], color='orange', label='Velocity Spikes', s=50)
        except Exception as e:
            print(f"Warning: Could not compute velocity spikes: {e}")

        # Enhanced annotations with key features
        ax1.annotate(f"Raw Path Length: {raw_features.get('path_length', 0):.2f}px",
                    xy=(0.02, 0.98), xycoords='axes fraction', fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax1.annotate(f"Duration: {raw_features.get('duration', 0):.3f}s",
                    xy=(0.02, 0.93), xycoords='axes fraction', fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax1.annotate(f"Velocity Mean: {raw_features.get('velocity_mean', 0):.1f}px/s",
                    xy=(0.02, 0.88), xycoords='axes fraction', fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax1.annotate(f"Direction Changes: {raw_features.get('direction_changes', 0)}",
                    xy=(0.02, 0.83), xycoords='axes fraction', fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))

        ax1.set_title("RAW DATA: Touch Path & Quality Issues", fontsize=14, fontweight='bold')
        ax1.legend(loc='upper right')
        ax1.grid(True, alpha=0.3)

        # === TOP RIGHT: PROCESSED FEATURES VALIDATION ===
        if processed_features:
            # Use same path but highlight processed feature accuracy
            sns.lineplot(x='x', y='y', data=raw_df, ax=ax2, marker='o', color='green', label='Processed Path', markersize=4)

            # Show bounding box for drag_area validation
            if processed_features.get('drag_area', 0) > 0:
                x_min, x_max = raw_df['x'].min(), raw_df['x'].max()
                y_min, y_max = raw_df['y'].min(), raw_df['y'].max()
                rect = patches.Rectangle((x_min, y_min), x_max-x_min, y_max-y_min,
                               linewidth=2, edgecolor='purple', facecolor='none', label='Bounding Box')
                ax2.add_patch(rect)

            # Enhanced processed feature annotations
            ax2.annotate(f"Processed Path Length: {processed_features.get('path_length', 0):.2f}px",
                        xy=(0.02, 0.98), xycoords='axes fraction', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
            ax2.annotate(f"Sequence Type: {processed_features.get('sequence_type', 'Unknown')}",
                        xy=(0.02, 0.93), xycoords='axes fraction', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
            ax2.annotate(f"Drag Area: {processed_features.get('drag_area', 0):.0f}px²",
                        xy=(0.02, 0.88), xycoords='axes fraction', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
            ax2.annotate(f"Path Straightness: {processed_features.get('path_straightness', 0):.3f}",
                        xy=(0.02, 0.83), xycoords='axes fraction', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
            ax2.annotate(f"Error Rate: {processed_features.get('error_rate', 0):.3f}",
                        xy=(0.02, 0.78), xycoords='axes fraction', fontsize=10,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))

            ax2.set_title("PROCESSED FEATURES: Extractor Output Validation", fontsize=14, fontweight='bold')
            ax2.legend(loc='upper right')
        else:
            ax2.text(0.5, 0.5, "No Processed Features Available", ha='center', va='center', fontsize=16)
            ax2.set_title("PROCESSED FEATURES: Not Available", fontsize=14, fontweight='bold')

        ax2.grid(True, alpha=0.3)

        # === BOTTOM LEFT: FEATURE COMPARISON TABLE ===
        ax3.axis('off')

        # Create detailed feature comparison
        comparison_data = []
        key_features = ['path_length', 'duration', 'velocity_mean', 'direction_changes', 'drag_area']

        for feature in key_features:
            raw_val = raw_features.get(feature, 0)
            proc_val = processed_features.get(feature, 0) if processed_features else 0

            # Calculate difference and percentage
            diff = abs(proc_val - raw_val) if raw_val != 0 else abs(proc_val)
            pct_diff = (diff / raw_val * 100) if raw_val != 0 else 0

            comparison_data.append([
                feature.replace('_', ' ').title(),
                f"{raw_val:.3f}" if isinstance(raw_val, float) else str(raw_val),
                f"{proc_val:.3f}" if isinstance(proc_val, float) else str(proc_val),
                f"{diff:.3f}",
                f"{pct_diff:.1f}%"
            ])

        # Create table
        table = ax3.table(cellText=comparison_data,
                         colLabels=['Feature', 'Raw Value', 'Processed Value', 'Abs Diff', '% Diff'],
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)

        # Color code the table
        for i in range(len(comparison_data)):
            pct_diff = float(comparison_data[i][4].replace('%', ''))
            if pct_diff < 5:  # Good agreement
                color = 'lightgreen'
            elif pct_diff < 15:  # Moderate difference
                color = 'lightyellow'
            else:  # Large difference - needs investigation
                color = 'lightcoral'

            for j in range(5):
                table[(i+1, j)].set_facecolor(color)

        ax3.set_title("FEATURE ACCURACY: Raw vs Processed Comparison", fontsize=14, fontweight='bold')

        # === BOTTOM RIGHT: KINEMATIC ANALYSIS ===
        if len(raw_df) > 2:
            try:
                # Calculate and plot velocity over time
                if 'velocity' in raw_df.columns:
                    ax4.plot(raw_df['time'], raw_df['velocity'], 'b-', label='Velocity', linewidth=2)
                    ax4.set_ylabel('Velocity (px/s)', color='b')
                    ax4.tick_params(axis='y', labelcolor='b')

                    # Add acceleration on secondary axis
                    ax4_twin = ax4.twinx()
                    if len(raw_df) > 3:
                        acceleration = raw_df['velocity'].diff() / raw_df['time'].diff()
                        ax4_twin.plot(raw_df['time'], acceleration, 'r-', label='Acceleration', linewidth=2)
                        ax4_twin.set_ylabel('Acceleration (px/s²)', color='r')
                        ax4_twin.tick_params(axis='y', labelcolor='r')

                    ax4.set_xlabel('Time (s)')
                    ax4.set_title("KINEMATIC ANALYSIS: Velocity & Acceleration", fontsize=14, fontweight='bold')
                    ax4.grid(True, alpha=0.3)

                    # Add kinematic feature annotations
                    if processed_features:
                        ax4.annotate(f"Vel Mean: {processed_features.get('velocity_mean', 0):.1f}px/s",
                                   xy=(0.02, 0.95), xycoords='axes fraction', fontsize=10,
                                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
                        ax4.annotate(f"Acc Mean: {processed_features.get('acc_mean', 0):.1f}px/s²",
                                   xy=(0.02, 0.88), xycoords='axes fraction', fontsize=10,
                                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
                        ax4.annotate(f"Jerk Mean: {processed_features.get('jerk_mean', 0):.1f}px/s³",
                                   xy=(0.02, 0.81), xycoords='axes fraction', fontsize=10,
                                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"))
                else:
                    ax4.text(0.5, 0.5, "Insufficient data for kinematic analysis", ha='center', va='center')
                    ax4.set_title("KINEMATIC ANALYSIS: Insufficient Data", fontsize=14, fontweight='bold')
            except Exception as e:
                ax4.text(0.5, 0.5, f"Kinematic analysis error: {str(e)}", ha='center', va='center')
                ax4.set_title("KINEMATIC ANALYSIS: Error", fontsize=14, fontweight='bold')
        else:
            ax4.text(0.5, 0.5, "Sequence too short for kinematic analysis", ha='center', va='center')
            ax4.set_title("KINEMATIC ANALYSIS: Sequence Too Short", fontsize=14, fontweight='bold')

        # Overall title with sequence info
        seq_type = processed_features.get('sequence_type', 'Unknown') if processed_features else 'Unknown'
        plt.suptitle(f"COMPREHENSIVE VALIDATION: Sequence {vis_data['sequence_id']} ({seq_type})",
                    fontsize=16, fontweight='bold')

        plt.tight_layout()

        # Save the enhanced plot
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"Enhanced validation plot saved to: {save_path}")
        else:
            default_path = f"validation_plot_seq_{vis_data['sequence_id']}.png"
            plt.savefig(default_path, dpi=150, bbox_inches='tight')
            print(f"Enhanced validation plot saved to: {default_path}")
        plt.close()

    def visualize_animation(self, vis_data: Dict, save_path: str = None):
        df = pd.DataFrame(vis_data['raw_events'])
        raw_features = vis_data['raw_features']
        abns = vis_data['abnormalities']
        
        # Compute velocity here to avoid KeyError (only if enough data)
        if len(df) > 1:
            df['delta_time'] = df['time'].diff()
            delta_d = np.sqrt(df['x'].diff()**2 + df['y'].diff()**2)
            df['velocity'] = delta_d / df['delta_time'].clip(lower=0.001)
        else:
            df['velocity'] = pd.Series([0] * len(df))  # Fallback for short sequences
        
        fig, ax = plt.subplots()
        line, = ax.plot([], [], 'o-', lw=2, color='blue')
        annot = ax.annotate("", xy=(0,0), xytext=(20,20), textcoords="offset points", bbox=dict(boxstyle="round", fc="w"))
        
        def update(frame):
            line.set_data(df['x'][:frame], df['y'][:frame])
            # Highlight abnormalities for human review (skip if no velocity data)
            if frame > 0 and (df['time'][frame] == df['time'][frame-1] or df['x'][frame] > 2000):
                annot.set_text("Abnormality: Duplicate or Outlier")
                annot.set_visible(True)
            elif frame > 1 and abs(df['velocity'][frame] - df['velocity'][frame-1]) > 1000:
                annot.set_text("Abnormality: Erratic Spike")
                annot.set_visible(True)
            else:
                annot.set_visible(False)
            ax.set_title(f"Raw Path Animation | Time: {df['time'][frame]:.2f}s | Raw Velocity: {raw_features.get('velocity_mean', 0):.2f}")
            return line, annot
        
        anim = FuncAnimation(fig, update, frames=len(df), init_func=lambda: (line.set_data([], []), annot.set_visible(False)), blit=False)
        if save_path:
            anim.save(save_path, writer='pillow')  # Use pillow instead of imagemagick
            print(f"Animation saved to: {save_path}")
        else:
            default_path = f"validation_animation_seq_{vis_data['sequence_id']}.gif"
            anim.save(default_path, writer='pillow')
            print(f"Animation saved to: {default_path}")
        plt.close()  # Close instead of show to avoid hanging

def run_dashboard(vis_data_list: List[Dict]):
    st.title("Feature Validation Dashboard - Human Review Tool")
    selected_seq = st.selectbox("Select Sequence for Validation", [d['sequence_id'] for d in vis_data_list])
    vis_data = next(d for d in vis_data_list if d['sequence_id'] == selected_seq)
    
    st.subheader("Raw Events Table (Inspect Complex Data)")
    st.dataframe(pd.DataFrame(vis_data['raw_events']))
    
    st.subheader("Feature Comparison Table (Raw vs. Processed)")
    comparison_data = {
        'Feature': ['duration', 'path_length', 'velocity_mean', 'direction_changes'],
        'Raw Value': [vis_data['raw_features'].get(k, 0) for k in ['duration', 'path_length', 'velocity_mean', 'direction_changes']],
        'Processed Value': [vis_data['processed_features'].get(k, 0) for k in ['duration', 'path_length', 'velocity_mean', 'direction_changes']]
    }
    st.table(pd.DataFrame(comparison_data))
    
    st.subheader("All Processed Features")
    st.json(vis_data['processed_features'])
    
    st.subheader("Flagged Abnormalities (Review Manually)")
    st.json(vis_data['abnormalities'])
    
    st.subheader("Interactive Raw Path (Zoom/Hover to Spot Issues)")
    fig = px.line(pd.DataFrame(vis_data['raw_events']), x='x', y='y', title="Raw Path with Hover Details", hover_data=['time', 'zone', 'color'])
    st.plotly_chart(fig)
    
    st.subheader("Validation Notes")
    notes = st.text_area("Add your observations (e.g., 'Erratic pattern at time X indicates multitouch')")
    if st.button("Save Notes"):
        st.success("Notes saved! (Implement file saving if needed)")

def generate_summary_report(all_vis_data: List[Dict]):
    """Generate a comprehensive validation summary focused on feature accuracy"""
    print("\n" + "="*80)
    print("COLORINGFEATUREEXTRACTOR VALIDATION SUMMARY")
    print("="*80)

    total_sequences = len(all_vis_data)
    print(f"📊 Total sequences validated: {total_sequences}")

    # Group by filename for file-level analysis
    files_processed = set(data.get('filename', 'unknown') for data in all_vis_data)
    print(f"📁 Files processed: {len(files_processed)}")
    for filename in sorted(files_processed):
        file_sequences = [d for d in all_vis_data if d.get('filename') == filename]
        print(f"   • {filename}: {len(file_sequences)} sequences")

    # Feature extraction validation
    successful_extractions = sum(1 for data in all_vis_data if data['processed_features'])
    success_rate = (successful_extractions / total_sequences) * 100 if total_sequences > 0 else 0
    print(f"\n🎯 FEATURE EXTRACTION VALIDATION:")
    print(f"   ✅ Success Rate: {success_rate:.1f}% ({successful_extractions}/{total_sequences})")

    if successful_extractions > 0:
        # Sequence type classification accuracy
        sequence_types = [data['processed_features'].get('sequence_type', 'Unknown') for data in all_vis_data if data['processed_features']]
        from collections import Counter
        type_counts = Counter(sequence_types)
        print(f"\n📋 SEQUENCE CLASSIFICATION:")
        for seq_type, count in type_counts.items():
            percentage = (count / successful_extractions) * 100
            print(f"   • {seq_type}: {count} sequences ({percentage:.1f}%)")

        # Feature accuracy analysis
        print(f"\n🔍 FEATURE ACCURACY ANALYSIS:")

        # Path length comparison
        path_length_diffs = []
        duration_diffs = []
        velocity_diffs = []

        for data in all_vis_data:
            if data['processed_features'] and data['raw_features']:
                raw_pl = data['raw_features'].get('path_length', 0)
                proc_pl = data['processed_features'].get('path_length', 0)
                if raw_pl > 0:
                    path_length_diffs.append(abs(proc_pl - raw_pl) / raw_pl * 100)

                raw_dur = data['raw_features'].get('duration', 0)
                proc_dur = data['processed_features'].get('duration', 0)
                if raw_dur > 0:
                    duration_diffs.append(abs(proc_dur - raw_dur) / raw_dur * 100)

                raw_vel = data['raw_features'].get('velocity_mean', 0)
                proc_vel = data['processed_features'].get('velocity_mean', 0)
                if raw_vel > 0:
                    velocity_diffs.append(abs(proc_vel - raw_vel) / raw_vel * 100)

        if path_length_diffs:
            print(f"   • Path Length Accuracy: {np.mean(path_length_diffs):.1f}% avg difference")
            accurate_pl = sum(1 for diff in path_length_diffs if diff < 5)
            print(f"     - {accurate_pl}/{len(path_length_diffs)} sequences within 5% accuracy")

        if duration_diffs:
            print(f"   • Duration Accuracy: {np.mean(duration_diffs):.1f}% avg difference")
            accurate_dur = sum(1 for diff in duration_diffs if diff < 1)
            print(f"     - {accurate_dur}/{len(duration_diffs)} sequences within 1% accuracy")

        if velocity_diffs:
            print(f"   • Velocity Accuracy: {np.mean(velocity_diffs):.1f}% avg difference")
            accurate_vel = sum(1 for diff in velocity_diffs if diff < 10)
            print(f"     - {accurate_vel}/{len(velocity_diffs)} sequences within 10% accuracy")

    # Data quality assessment
    total_duplicates = sum(data['abnormalities']['duplicates'] for data in all_vis_data)
    total_outliers = sum(data['abnormalities']['outliers'] for data in all_vis_data)
    total_spikes = sum(data['abnormalities']['erratic_spikes'] for data in all_vis_data)

    print(f"\n⚠️  DATA QUALITY ASSESSMENT:")
    print(f"   • Duplicate timestamps: {total_duplicates} detected")
    print(f"   • Coordinate outliers: {total_outliers} detected")
    print(f"   • Erratic velocity spikes: {total_spikes} detected")

    # Validation recommendations
    print(f"\n📋 MANUAL VALIDATION RECOMMENDATIONS:")
    if success_rate < 100:
        print(f"   ⚠️  {total_sequences - successful_extractions} sequences failed feature extraction - investigate")

    if path_length_diffs and np.mean(path_length_diffs) > 10:
        print(f"   ⚠️  High path length differences detected - review smoothing algorithms")

    if total_spikes > total_sequences * 10:  # More than 10 spikes per sequence on average
        print(f"   ⚠️  High velocity spike count - may indicate sensor noise")

    if total_outliers > 0:
        print(f"   ⚠️  Coordinate outliers detected - check sensor calibration")

    print(f"\n📁 GENERATED VALIDATION FILES:")
    print(f"   • Individual plots: validation_[filename]_seq_[id].png")
    print(f"   • Total plots created: {total_sequences}")
    print(f"   • HTML dashboard: validation_dashboard.html (if generated)")

    print("\n" + "="*80)

def generate_html_dashboard(all_vis_data: List[Dict]):
    """Generate an HTML dashboard for viewing validation results"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Feature Validation Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .sequence { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .features { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .feature-box { background-color: #f9f9f9; padding: 10px; border-radius: 3px; }
        .abnormality { color: red; font-weight: bold; }
        .success { color: green; }
        .plot-link { display: inline-block; margin: 5px; padding: 10px; background-color: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .plot-link:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Feature Validation Dashboard</h1>
        <p>Interactive validation results for touch sequence analysis</p>
    </div>
"""

    # Add summary statistics
    total_sequences = len(all_vis_data)
    total_abnormalities = sum(sum(data['abnormalities'].values()) for data in all_vis_data)

    html_content += f"""
    <div class="sequence">
        <h2>Summary Statistics</h2>
        <p><strong>Total Sequences:</strong> {total_sequences}</p>
        <p><strong>Total Abnormalities:</strong> {total_abnormalities}</p>
        <p><strong>Success Rate:</strong> {(len([d for d in all_vis_data if d['processed_features']]) / total_sequences * 100):.1f}%</p>
    </div>
"""

    # Add individual sequence details
    for i, vis_data in enumerate(all_vis_data[:10]):  # Limit to first 10 for performance
        seq_id = vis_data['sequence_id']
        raw_features = vis_data['raw_features']
        processed_features = vis_data['processed_features']
        abnormalities = vis_data['abnormalities']

        html_content += f"""
    <div class="sequence">
        <h3>Sequence {seq_id}</h3>
        <div class="features">
            <div class="feature-box">
                <h4>Raw Features</h4>
                <p>Duration: {raw_features.get('duration', 0):.3f}s</p>
                <p>Path Length: {raw_features.get('path_length', 0):.2f}px</p>
                <p>Velocity: {raw_features.get('velocity_mean', 0):.2f}px/s</p>
                <p>Direction Changes: {raw_features.get('direction_changes', 0)}</p>
            </div>
            <div class="feature-box">
                <h4>Processed Features</h4>
                <p>Duration: {processed_features.get('duration', 0):.3f}s</p>
                <p>Path Length: {processed_features.get('path_length', 0):.2f}px</p>
                <p>Velocity: {processed_features.get('velocity_mean', 0):.2f}px/s</p>
                <p>Type: {processed_features.get('sequence_type', 'Unknown')}</p>
            </div>
        </div>
        <div class="feature-box">
            <h4>Data Quality</h4>
            <p class="{'abnormality' if abnormalities['duplicates'] > 0 else 'success'}">
                Duplicates: {abnormalities['duplicates']}
            </p>
            <p class="{'abnormality' if abnormalities['outliers'] > 0 else 'success'}">
                Outliers: {abnormalities['outliers']}
            </p>
            <p class="{'abnormality' if abnormalities['erratic_spikes'] > 0 else 'success'}">
                Erratic Spikes: {abnormalities['erratic_spikes']}
            </p>
        </div>
        <a href="validation_plot_seq_{seq_id}.png" class="plot-link" target="_blank">View Plot</a>
    </div>
"""

    html_content += """
</body>
</html>
"""

    # Save HTML file
    with open('validation_dashboard.html', 'w') as f:
        f.write(html_content)

    print("HTML dashboard generated: validation_dashboard.html")
    print("Open this file in your web browser to view the interactive dashboard.")

def main():
    parser = argparse.ArgumentParser(description="Feature Validation - Human Comparison Tool")
    parser.add_argument('--raw', action='store_true', help='Use raw mode')
    parser.add_argument('--vis', choices=['static', 'animation', 'dashboard', 'summary', 'html'], default='static', help='Visualization type')
    parser.add_argument('--show-plots', action='store_true', help='Show plots in GUI (may hang on some systems)')
    args = parser.parse_args()
    
    pipeline = FeatureValidation(raw_mode=args.raw)
    directory = Path('raw_data')
    json_files = list(directory.glob("*.json"))

    if not json_files:
        print("❌ No JSON files found in raw_data directory!")
        return

    print(f"📁 Found {len(json_files)} JSON file(s) in raw_data directory:")
    for i, json_file in enumerate(json_files, 1):
        print(f"   {i}. {json_file.name}")

    all_vis_data = []
    total_sequences = 0

    # Process each JSON file
    for file_idx, json_file in enumerate(json_files, 1):
        print(f"\n🔍 Processing file {file_idx}/{len(json_files)}: {json_file.name}")

        try:
            touch_data = pipeline.load_json(str(json_file))
            file_sequences = 0

            for seq_id, events in touch_data.items():
                if len(events) < 2:  # Skip sequences too short for meaningful visualization
                    print(f"   ⚠️  Skipping sequence {seq_id}: too short ({len(events)} events)")
                    continue

                vis_data = pipeline.prepare_vis_data(events, int(seq_id))
                vis_data['filename'] = json_file.name  # Add filename for tracking
                all_vis_data.append(vis_data)
                file_sequences += 1
                total_sequences += 1

            print(f"   ✅ Processed {file_sequences} sequences from {json_file.name}")

        except Exception as e:
            print(f"   ❌ Error processing {json_file.name}: {e}")
            continue

    print(f"\n📊 Total sequences ready for validation: {total_sequences}")

    if total_sequences == 0:
        print("❌ No valid sequences found for validation!")
        return
    
    if args.vis == 'static':
        print(f"\n🎯 MANUAL VALIDATION MODE: Generating plots for {total_sequences} sequences")
        print("📋 Each plot shows:")
        print("   • Top Left: Raw data path with quality indicators")
        print("   • Top Right: Processed features validation")
        print("   • Bottom Left: Feature accuracy comparison table")
        print("   • Bottom Right: Kinematic analysis")
        print("\n🔍 Review each plot to validate ColoringFeatureExtractor accuracy...")

        # Group by filename for organized validation
        sequences_by_file = {}
        for vis_data in all_vis_data:
            filename = vis_data.get('filename', 'unknown')
            if filename not in sequences_by_file:
                sequences_by_file[filename] = []
            sequences_by_file[filename].append(vis_data)

        # Process each file's sequences
        for filename, file_sequences in sequences_by_file.items():
            print(f"\n📄 Validating {len(file_sequences)} sequences from {filename}")

            for i, vis_data in enumerate(file_sequences, 1):
                seq_id = vis_data['sequence_id']
                seq_type = vis_data.get('processed_features', {}).get('sequence_type', 'Unknown')

                print(f"   🔍 Generating plot {i}/{len(file_sequences)}: Sequence {seq_id} ({seq_type})")

                # Create filename-specific plot name
                plot_name = f"validation_{filename.replace('.json', '')}_seq_{seq_id}.png"
                pipeline.visualize_static(vis_data, plot_name)

        print(f"\n✅ Generated {total_sequences} validation plots")
        print("📁 Plot files saved with format: validation_[filename]_seq_[id].png")
        print("\n🎯 MANUAL VALIDATION CHECKLIST:")
        print("   ✓ Check if raw vs processed path_length values match")
        print("   ✓ Verify sequence_type classification (Tap/Drag/Hold)")
        print("   ✓ Validate drag_area bounding box visualization")
        print("   ✓ Review kinematic features (velocity, acceleration)")
        print("   ✓ Look for data quality issues (outliers, spikes)")

        generate_summary_report(all_vis_data)
    elif args.vis == 'animation':
        for vis_data in all_vis_data:
            pipeline.visualize_animation(vis_data)
        generate_summary_report(all_vis_data)
    elif args.vis == 'summary':
        generate_summary_report(all_vis_data)
    elif args.vis == 'html':
        generate_html_dashboard(all_vis_data)
        generate_summary_report(all_vis_data)
    elif args.vis == 'dashboard':
        run_dashboard(all_vis_data)

if __name__ == "__main__":
    main()
