#!/usr/bin/env python3
"""
Test script for the enhanced medical evaluation summary panel
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def test_medical_evaluation_panel():
    """Test the enhanced summary panel with medical evaluation notes"""
    print("🏥 Testing medical evaluation summary panel...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Test different types of sequences for medical evaluation
    test_sequences = []
    
    # Find sequences with different characteristics
    for seq_id, events in touch_data.items():
        if len(events) >= 10:  # Minimum viable sequence
            test_sequences.append((seq_id, events))
            if len(test_sequences) >= 3:  # Test first 3 viable sequences
                break
    
    if not test_sequences:
        print("❌ No viable sequences found!")
        return
    
    print(f"🧪 Testing {len(test_sequences)} sequences for medical evaluation")
    
    # Process sequences and generate medical evaluation plots
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    generated_files = []
    evaluation_results = []
    
    for i, (seq_id, events) in enumerate(test_sequences):
        print(f"\n📊 Processing sequence {seq_id} ({len(events)} events)...")
        
        # Calculate features
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if not processed_features:
            print(f"   ⚠️  Could not process sequence {seq_id}")
            continue
        
        seq_data = {
            'sequence_id': int(seq_id),
            'raw_features': raw_features,
            'processed_features': processed_features,
            'events': events
        }
        
        # Generate plot with medical evaluation
        plot_file = f"medical_eval_{base_name}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(
                seq_data, bucket_key, bucket_info, plot_file, base_name
            )
            generated_files.append(output_file)
            
            # Calculate evaluation metrics for reporting
            processed_events = validator.get_processed_path_data(events, int(seq_id))
            path_stats = validator.calculate_path_statistics(events, processed_events)
            
            events_removed = len(events) - len(processed_events)
            length_change_pct = abs(path_stats['length_change'])
            
            # Medical evaluation logic (same as in the method)
            if length_change_pct < 5 and events_removed < len(events) * 0.05:
                clinical_suitability = "Excellent for clinical use"
                validation_required = "No"
                smoothing_impact = "Negligible"
            elif length_change_pct < 10 and events_removed < len(events) * 0.1:
                clinical_suitability = "Suitable with documentation"
                validation_required = "Recommended"
                smoothing_impact = "Minimal"
            else:
                clinical_suitability = "Requires clinical review"
                validation_required = "Yes - mandatory"
                smoothing_impact = "Significant"
            
            evaluation_results.append({
                'sequence_id': seq_id,
                'events_count': len(events),
                'events_removed': events_removed,
                'length_change': length_change_pct,
                'clinical_suitability': clinical_suitability,
                'validation_required': validation_required,
                'smoothing_impact': smoothing_impact,
                'file': output_file
            })
            
            print(f"   ✅ Generated: {output_file}")
            print(f"   🏥 Clinical Assessment: {clinical_suitability}")
            print(f"   📊 Length Change: {length_change_pct:.1f}%")
            print(f"   🔍 Validation Required: {validation_required}")
            
        except Exception as e:
            print(f"   ❌ Failed to generate plot: {e}")
            import traceback
            traceback.print_exc()
    
    # Open the first generated plot
    if generated_files:
        first_plot = generated_files[0]
        print(f"\n🌐 Opening medical evaluation plot: {first_plot}")
        try:
            plot_path = os.path.abspath(first_plot)
            webbrowser.open(f"file://{plot_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    # Print comprehensive medical evaluation summary
    print(f"\n🏥 Medical Evaluation Summary")
    print("=" * 50)
    
    for result in evaluation_results:
        print(f"\n📊 Sequence {result['sequence_id']}:")
        print(f"   📏 Events: {result['events_count']} → {result['events_count'] - result['events_removed']}")
        print(f"   📉 Events Removed: {result['events_removed']} ({result['events_removed']/result['events_count']*100:.1f}%)")
        print(f"   📐 Length Change: {result['length_change']:.1f}%")
        print(f"   🎯 Smoothing Impact: {result['smoothing_impact']}")
        print(f"   🏥 Clinical Suitability: {result['clinical_suitability']}")
        print(f"   ✅ Validation Required: {result['validation_required']}")
    
    print(f"\n📋 Clinical Guidelines Applied:")
    print(f"   🟢 Excellent (< 5% change, < 5% events removed)")
    print(f"   🟡 Suitable (< 10% change, < 10% events removed)")
    print(f"   🔴 Review Required (≥ 10% change or ≥ 10% events removed)")
    
    print(f"\n📂 Generated Medical Evaluation Files:")
    for file in generated_files:
        print(f"   🏥 {file}")

def test_clinical_thresholds():
    """Test different clinical threshold scenarios"""
    print("\n🧪 Testing Clinical Threshold Scenarios...")
    
    # Define test scenarios
    scenarios = [
        {"name": "Excellent Clinical Quality", "length_change": 2.5, "events_removed_pct": 0.02},
        {"name": "Good Clinical Quality", "length_change": 7.5, "events_removed_pct": 0.08},
        {"name": "Requires Clinical Review", "length_change": 15.0, "events_removed_pct": 0.15},
    ]
    
    print("\n📊 Clinical Assessment Scenarios:")
    for scenario in scenarios:
        length_change = scenario["length_change"]
        events_removed_pct = scenario["events_removed_pct"]
        
        # Apply same logic as in the method
        if length_change < 5 and events_removed_pct < 0.05:
            clinical_suitability = "Excellent for clinical use"
            validation_required = "No"
            smoothing_impact = "Negligible - preserves behavioral patterns"
        elif length_change < 10 and events_removed_pct < 0.1:
            clinical_suitability = "Suitable with documentation"
            validation_required = "Recommended"
            smoothing_impact = "Minimal - acceptable for most analyses"
        else:
            clinical_suitability = "Requires clinical review"
            validation_required = "Yes - mandatory"
            smoothing_impact = "Significant - may affect behavioral metrics"
        
        print(f"\n🎯 {scenario['name']}:")
        print(f"   📐 Length Change: {length_change}%")
        print(f"   📉 Events Removed: {events_removed_pct*100:.1f}%")
        print(f"   🎯 Smoothing Impact: {smoothing_impact}")
        print(f"   🏥 Clinical Suitability: {clinical_suitability}")
        print(f"   ✅ Validation Required: {validation_required}")

if __name__ == "__main__":
    test_medical_evaluation_panel()
    test_clinical_thresholds()
