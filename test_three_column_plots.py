#!/usr/bin/env python3
"""
Test script for the enhanced three-column path visualization
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def test_three_column_visualization():
    """Test the enhanced individual sequence plots with three-column layout"""
    print("🎨 Testing three-column path visualization...")
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    print(f"📁 Using file: {selected_file.name}")
    
    # Load data
    touch_data = validator.load_json_data(selected_file)
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process first few sequences for testing
    sequences_data = []
    for seq_id, events in list(touch_data.items())[:3]:  # Test first 3 sequences
        if len(events) < 2:
            continue
        
        print(f"   Processing sequence {seq_id}...")
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Processed {len(sequences_data)} sequences")
    
    # Test spatial features bucket with three-column visualization
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    print(f"🎨 Testing three-column individual plots...")
    
    # Generate three-column individual plots
    generated_files = []
    for seq_data in sequences_data:
        seq_id = seq_data['sequence_id']
        plot_file = f"three_column_{base_name}_{bucket_key}_seq_{seq_id}.html"
        
        try:
            output_file = validator.create_individual_sequence_plot(
                seq_data, bucket_key, bucket_info, plot_file, base_name
            )
            generated_files.append(output_file)
            print(f"   ✅ Three-column sequence {seq_id}: {output_file}")
        except Exception as e:
            print(f"   ❌ Sequence {seq_id} failed: {e}")
            import traceback
            traceback.print_exc()
    
    if generated_files:
        # Open the first three-column plot
        first_plot = generated_files[0]
        print(f"\n🌐 Opening three-column plot in browser: {first_plot}")
        try:
            plot_path = os.path.abspath(first_plot)
            webbrowser.open(f"file://{plot_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    print(f"\n🎉 Three-column path visualization test complete!")
    print(f"📂 Generated three-column files:")
    for file in generated_files:
        print(f"   📊 {file}")
    
    print(f"\n📋 Three-Column Features:")
    print(f"   🎯 Column 1: Raw touch path only")
    print(f"   🔄 Column 2: Processed touch path only")
    print(f"   📊 Column 3: Overlay comparison with both paths")
    print(f"   🎨 Professional color scheme throughout")
    print(f"   📈 Legend for overlay comparison")
    print(f"   🔍 Interactive zoom/pan capabilities")
    print(f"   📏 Increased width (1200px) for better visibility")

def test_overlay_comparison_details():
    """Test specific details of the overlay comparison"""
    print("\n🔍 Testing overlay comparison details...")
    
    validator = SimpleFeatureValidator()
    files = validator.get_available_files()
    
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    base_name = selected_file.stem
    touch_data = validator.load_json_data(selected_file)
    
    # Find a sequence with good length for demonstration
    best_sequence = None
    target_length = 50  # Looking for sequences with around 50 events
    
    for seq_id, events in touch_data.items():
        if 30 <= len(events) <= 100:  # Good range for visualization
            best_sequence = (seq_id, events)
            break
    
    if not best_sequence:
        # Fallback to any sequence
        best_sequence = list(touch_data.items())[0]
    
    seq_id, events = best_sequence
    print(f"📊 Testing overlay with sequence {seq_id} ({len(events)} events)")
    
    # Process the sequence
    raw_features = validator.calculate_raw_features(events, int(seq_id))
    processed_features = validator.get_processed_features(events, int(seq_id))
    
    seq_data = {
        'sequence_id': int(seq_id),
        'raw_features': raw_features,
        'processed_features': processed_features,
        'events': events
    }
    
    # Generate overlay comparison plot
    bucket_key = 'spatial'
    bucket_info = validator.FEATURE_BUCKETS[bucket_key]
    
    plot_file = f"overlay_test_{base_name}_{bucket_key}_seq_{seq_id}.html"
    
    try:
        output_file = validator.create_individual_sequence_plot(
            seq_data, bucket_key, bucket_info, plot_file, base_name
        )
        print(f"✅ Generated overlay test: {output_file}")
        
        # Open the plot
        plot_path = os.path.abspath(output_file)
        webbrowser.open(f"file://{plot_path}")
        print(f"🌐 Opened overlay comparison test")
        
        # Print sequence details
        print(f"\n📋 Overlay Test Details:")
        print(f"   📏 Raw Events: {len(events)}")
        processed_events = validator.get_processed_path_data(events, int(seq_id))
        print(f"   🔄 Processed Events: {len(processed_events)}")
        print(f"   📊 Events Removed: {len(events) - len(processed_events)}")
        print(f"   🎯 Path Length: {raw_features.get('path_length', 0):.1f}px")
        print(f"   ⏱️  Duration: {raw_features.get('duration', 0):.2f}s")
        
    except Exception as e:
        print(f"❌ Failed to generate overlay test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_three_column_visualization()
    test_overlay_comparison_details()
