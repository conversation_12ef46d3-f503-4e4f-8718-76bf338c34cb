#!/usr/bin/env python3
"""
Demo script to showcase the enhanced dashboard functionality
"""

from simple_feature_validator import SimpleFeatureValidator
from pathlib import Path
import webbrowser
import os

def demo_all_buckets():
    """Generate dashboards for all feature buckets"""
    print("🎯 DASHBOARD DEMO - GENERATING ALL FEATURE BUCKETS")
    print("=" * 60)
    
    # Initialize validator
    validator = SimpleFeatureValidator()
    
    # Get the first available file
    files = validator.get_available_files()
    if not files:
        print("❌ No JSON files found in raw_data/ directory!")
        return
    
    selected_file = files[0]
    print(f"📁 Using file: {selected_file.name}")
    
    # Load and process data
    print(f"🔍 Loading data...")
    touch_data = validator.load_json_data(selected_file)
    
    if not touch_data:
        print("❌ No valid touch data found!")
        return
    
    print(f"📊 Found {len(touch_data)} sequences")
    
    # Process all sequences
    sequences_data = []
    successful_extractions = 0
    
    for seq_id, events in touch_data.items():
        if len(events) < 2:
            continue
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
            successful_extractions += 1
    
    if not sequences_data:
        print("❌ No sequences could be processed!")
        return
    
    print(f"✅ Successfully processed {successful_extractions} sequences")
    
    base_name = selected_file.stem
    generated_dashboards = []
    
    # Generate dashboards for all buckets
    for bucket_key, bucket_info in validator.FEATURE_BUCKETS.items():
        print(f"\n🎨 Generating {bucket_info['name']} dashboard...")
        
        try:
            # Generate main dashboard
            dashboard_file = validator.create_dashboard_html(sequences_data, bucket_key, bucket_info, base_name)
            print(f"   ✅ Dashboard: {dashboard_file}")
            generated_dashboards.append((bucket_key, bucket_info['name'], dashboard_file))
            
            # Generate individual sequence plots (first 10 for demo)
            individual_count = 0
            for seq_data in sequences_data[:10]:
                seq_id = seq_data['sequence_id']
                plot_file = f"validation_{base_name}_{bucket_key}_seq_{seq_id}.html"
                
                try:
                    validator.create_individual_sequence_plot(seq_data, bucket_key, bucket_info, plot_file)
                    individual_count += 1
                except Exception as e:
                    print(f"   ⚠️  Sequence {seq_id} plot failed: {e}")
            
            print(f"   📊 Generated {individual_count} individual plots")
            
        except Exception as e:
            print(f"   ❌ Dashboard generation failed: {e}")
    
    # Summary and navigation
    print(f"\n🎉 DEMO COMPLETE!")
    print(f"📂 Generated {len(generated_dashboards)} feature bucket dashboards:")
    
    for bucket_key, bucket_name, dashboard_file in generated_dashboards:
        print(f"   🎯 {bucket_name}: {dashboard_file}")
    
    # Open the first dashboard automatically
    if generated_dashboards:
        first_dashboard = generated_dashboards[0][2]
        print(f"\n🌐 Opening first dashboard in browser: {first_dashboard}")
        try:
            dashboard_path = os.path.abspath(first_dashboard)
            webbrowser.open(f"file://{dashboard_path}")
        except Exception as e:
            print(f"⚠️  Could not open browser: {e}")
    
    print(f"\n📋 NAVIGATION GUIDE:")
    print(f"   1. Each dashboard shows overview statistics and sequence list")
    print(f"   2. Click 'View Validation Plot' for detailed feature comparison")
    print(f"   3. Individual plots show raw vs processed values for each feature")
    print(f"   4. Use different bucket dashboards to focus on specific feature types")
    
    return generated_dashboards

def demo_specific_bucket(bucket_name='spatial'):
    """Generate dashboard for a specific bucket and open it"""
    print(f"🎯 GENERATING {bucket_name.upper()} FEATURES DASHBOARD")
    print("=" * 50)
    
    validator = SimpleFeatureValidator()
    files = validator.get_available_files()
    
    if not files:
        print("❌ No JSON files found!")
        return
    
    selected_file = files[0]
    touch_data = validator.load_json_data(selected_file)
    
    # Process sequences
    sequences_data = []
    for seq_id, events in touch_data.items():
        if len(events) < 2:
            continue
        
        raw_features = validator.calculate_raw_features(events, int(seq_id))
        processed_features = validator.get_processed_features(events, int(seq_id))
        
        if processed_features:
            sequences_data.append({
                'sequence_id': int(seq_id),
                'raw_features': raw_features,
                'processed_features': processed_features,
                'events': events
            })
    
    if bucket_name not in validator.FEATURE_BUCKETS:
        print(f"❌ Unknown bucket: {bucket_name}")
        print(f"Available buckets: {list(validator.FEATURE_BUCKETS.keys())}")
        return
    
    bucket_info = validator.FEATURE_BUCKETS[bucket_name]
    base_name = selected_file.stem
    
    # Generate dashboard and plots
    dashboard_file = validator.create_dashboard_html(sequences_data, bucket_name, bucket_info, base_name)
    
    # Generate all individual plots
    for seq_data in sequences_data:
        seq_id = seq_data['sequence_id']
        plot_file = f"validation_{base_name}_{bucket_name}_seq_{seq_id}.html"
        validator.create_individual_sequence_plot(seq_data, bucket_name, bucket_info, plot_file)
    
    print(f"✅ Generated dashboard with {len(sequences_data)} sequence plots")
    
    # Open dashboard
    try:
        dashboard_path = os.path.abspath(dashboard_file)
        webbrowser.open(f"file://{dashboard_path}")
        print(f"🌐 Opened dashboard: {dashboard_file}")
    except Exception as e:
        print(f"⚠️  Could not open browser: {e}")
        print(f"📂 Please manually open: {dashboard_file}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        bucket_name = sys.argv[1]
        demo_specific_bucket(bucket_name)
    else:
        demo_all_buckets()
